{"name": "salonier", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.12", "expo-blur": "^14.1.5", "expo-calendar": "^14.1.4", "expo-camera": "^16.1.8", "expo-contacts": "^14.2.5", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "^2.26.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}