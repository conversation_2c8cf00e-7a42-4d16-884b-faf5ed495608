import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { HairZoneAnalysis } from '../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

interface Props {
  zoneAnalysis: HairZoneAnalysis[];
  capturedImages: { id: string; uri: string; angle: string }[];
  onZoneSelect: (zone: 'roots' | 'mids' | 'ends') => void;
  selectedZone: 'roots' | 'mids' | 'ends';
}

const ZoneAnalysisDisplay: React.FC<Props> = ({
  zoneAnalysis,
  capturedImages,
  onZoneSelect,
  selectedZone,
}) => {
  const getZoneData = (zone: 'roots' | 'mids' | 'ends'): HairZoneAnalysis | undefined => {
    return zoneAnalysis.find(z => z.zone === zone);
  };

  const getZoneIcon = (zone: 'roots' | 'mids' | 'ends'): string => {
    switch (zone) {
      case 'roots': return 'sprout';
      case 'mids': return 'hair-dryer';
      case 'ends': return 'scissors-cutting';
    }
  };

  const getZoneName = (zone: 'roots' | 'mids' | 'ends'): string => {
    switch (zone) {
      case 'roots': return 'Raíces';
      case 'mids': return 'Medios';
      case 'ends': return 'Puntas';
    }
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return COLORS.success;
    if (confidence >= 0.6) return COLORS.warning;
    return COLORS.error;
  };

  const ZoneSelector: React.FC = () => (
    <View style={styles.zoneSelector}>
      {(['roots', 'mids', 'ends'] as const).map((zone) => {
        const zoneData = getZoneData(zone);
        const isSelected = selectedZone === zone;
        
        return (
          <TouchableOpacity
            key={zone}
            style={[
              styles.zoneButton,
              isSelected && styles.zoneButtonSelected,
              !zoneData && styles.zoneButtonDisabled,
            ]}
            onPress={() => onZoneSelect(zone)}
            disabled={!zoneData}
          >
            <MaterialCommunityIcons 
              name={getZoneIcon(zone) as any} 
              size={24} 
              color={isSelected ? COLORS.textInverse : COLORS.primary}
            />
            <Text style={[
              styles.zoneButtonText,
              isSelected && styles.zoneButtonTextSelected,
            ]}>
              {getZoneName(zone)}
            </Text>
            {zoneData && (
              <View style={[
                styles.confidenceIndicator,
                { backgroundColor: getConfidenceColor(zoneData.naturalLevel.confidence) }
              ]} />
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  const ZoneDetails: React.FC<{ zoneData: HairZoneAnalysis }> = ({ zoneData }) => (
    <ScrollView style={styles.zoneDetails} showsVerticalScrollIndicator={false}>
      {/* Información principal */}
      <View style={styles.detailSection}>
        <Text style={styles.sectionTitle}>Análisis Principal</Text>
        
        <View style={styles.parameterCard}>
          <View style={styles.parameterHeader}>
            <MaterialCommunityIcons name="numeric" size={20} color={COLORS.primary} />
            <Text style={styles.parameterTitle}>Nivel Natural</Text>
            <View style={[
              styles.confidenceBadge,
              { backgroundColor: getConfidenceColor(zoneData.naturalLevel.confidence) + '20' }
            ]}>
              <Text style={[
                styles.confidenceText,
                { color: getConfidenceColor(zoneData.naturalLevel.confidence) }
              ]}>
                {Math.round(zoneData.naturalLevel.confidence * 100)}%
              </Text>
            </View>
          </View>
          <Text style={styles.parameterValue}>
            Nivel {zoneData.naturalLevel.value} - {zoneData.naturalLevel.description}
          </Text>
        </View>

        <View style={styles.parameterCard}>
          <View style={styles.parameterHeader}>
            <MaterialCommunityIcons name="palette" size={20} color={COLORS.secondary} />
            <Text style={styles.parameterTitle}>Subtono</Text>
          </View>
          <Text style={styles.parameterValue}>
            {zoneData.undertone.value === 'warm' ? 'Cálido' : 
             zoneData.undertone.value === 'cool' ? 'Frío' : 'Neutro'}
          </Text>
          <Text style={styles.parameterDescription}>
            {zoneData.undertone.description}
          </Text>
        </View>

        {zoneData.artificialColor.detected && (
          <View style={styles.parameterCard}>
            <View style={styles.parameterHeader}>
              <MaterialCommunityIcons name="brush" size={20} color={COLORS.accent} />
              <Text style={styles.parameterTitle}>Color Artificial Detectado</Text>
            </View>
            <Text style={styles.parameterValue}>
              Nivel {zoneData.artificialColor.level} - {zoneData.artificialColor.tone}
            </Text>
            {zoneData.artificialColor.ageWeeks && (
              <Text style={styles.parameterDescription}>
                Aplicado hace {zoneData.artificialColor.ageWeeks} semanas
              </Text>
            )}
            <Text style={styles.parameterDescription}>
              Desvanecimiento: {zoneData.artificialColor.fadeLevel}
            </Text>
          </View>
        )}
      </View>

      {/* Características técnicas */}
      <View style={styles.detailSection}>
        <Text style={styles.sectionTitle}>Características Técnicas</Text>
        
        <View style={styles.technicalGrid}>
          <View style={styles.technicalItem}>
            <Text style={styles.technicalLabel}>Porosidad</Text>
            <Text style={styles.technicalValue}>
              {zoneData.porosity.value === 'low' ? 'Baja' :
               zoneData.porosity.value === 'medium' ? 'Media' : 'Alta'}
            </Text>
            <Text style={styles.technicalSubtext}>
              {zoneData.porosity.uniformity === 'even' ? 'Uniforme' : 'Desigual'}
            </Text>
          </View>
          
          <View style={styles.technicalItem}>
            <Text style={styles.technicalLabel}>Elasticidad</Text>
            <Text style={styles.technicalValue}>
              {zoneData.elasticity.value === 'poor' ? 'Pobre' :
               zoneData.elasticity.value === 'normal' ? 'Normal' : 'Excelente'}
            </Text>
            {zoneData.elasticity.stretchTest && (
              <Text style={styles.technicalSubtext}>Test físico realizado</Text>
            )}
          </View>
        </View>
      </View>

      {/* Condición */}
      <View style={styles.detailSection}>
        <Text style={styles.sectionTitle}>Condición del Cabello</Text>
        
        <View style={styles.conditionGrid}>
          <View style={styles.conditionItem}>
            <Text style={styles.conditionLabel}>Daño</Text>
            <View style={[
              styles.conditionIndicator,
              { backgroundColor: 
                zoneData.condition.damage === 'none' ? COLORS.success :
                zoneData.condition.damage === 'minimal' ? COLORS.info :
                zoneData.condition.damage === 'moderate' ? COLORS.warning : COLORS.error
              }
            ]}>
              <Text style={styles.conditionText}>
                {zoneData.condition.damage === 'none' ? 'Sin daño' :
                 zoneData.condition.damage === 'minimal' ? 'Mínimo' :
                 zoneData.condition.damage === 'moderate' ? 'Moderado' : 'Severo'}
              </Text>
            </View>
          </View>
          
          <View style={styles.conditionItem}>
            <Text style={styles.conditionLabel}>Sequedad</Text>
            <View style={[
              styles.conditionIndicator,
              { backgroundColor: 
                zoneData.condition.dryness === 'none' ? COLORS.success :
                zoneData.condition.dryness === 'mild' ? COLORS.info :
                zoneData.condition.dryness === 'moderate' ? COLORS.warning : COLORS.error
              }
            ]}>
              <Text style={styles.conditionText}>
                {zoneData.condition.dryness === 'none' ? 'Hidratado' :
                 zoneData.condition.dryness === 'mild' ? 'Leve' :
                 zoneData.condition.dryness === 'moderate' ? 'Moderada' : 'Severa'}
              </Text>
            </View>
          </View>
          
          <View style={styles.conditionItem}>
            <Text style={styles.conditionLabel}>Rotura</Text>
            <View style={[
              styles.conditionIndicator,
              { backgroundColor: 
                zoneData.condition.breakage === 'none' ? COLORS.success :
                zoneData.condition.breakage === 'minimal' ? COLORS.info :
                zoneData.condition.breakage === 'moderate' ? COLORS.warning : COLORS.error
              }
            ]}>
              <Text style={styles.conditionText}>
                {zoneData.condition.breakage === 'none' ? 'Sin rotura' :
                 zoneData.condition.breakage === 'minimal' ? 'Mínima' :
                 zoneData.condition.breakage === 'moderate' ? 'Moderada' : 'Severa'}
              </Text>
            </View>
          </View>
        </View>

        {(zoneData.condition.chemicalResidues || zoneData.condition.metalDeposits) && (
          <View style={styles.warningContainer}>
            <MaterialCommunityIcons name="alert-circle" size={20} color={COLORS.warning} />
            <View style={styles.warningContent}>
              <Text style={styles.warningTitle}>Alertas Químicas</Text>
              {zoneData.condition.chemicalResidues && (
                <Text style={styles.warningText}>• Residuos químicos detectados</Text>
              )}
              {zoneData.condition.metalDeposits && (
                <Text style={styles.warningText}>• Depósitos metálicos detectados</Text>
              )}
            </View>
          </View>
        )}
      </View>

      {/* Información específica por zona */}
      {zoneData.zone === 'roots' && zoneData.rootsSpecific && (
        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Análisis de Raíces</Text>
          <View style={styles.specificInfo}>
            <Text style={styles.specificItem}>
              Crecimiento: {zoneData.rootsSpecific.growthLength}mm
            </Text>
            <Text style={styles.specificItem}>
              Canas: {zoneData.rootsSpecific.grayPercentage}% ({zoneData.rootsSpecific.grayDistribution})
            </Text>
            <Text style={styles.specificItem}>
              Textura natural: {zoneData.rootsSpecific.naturalTexture}
            </Text>
          </View>
        </View>
      )}

      {zoneData.zone === 'mids' && zoneData.midsSpecific && (
        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Análisis de Medios</Text>
          <View style={styles.specificInfo}>
            <Text style={styles.specificItem}>
              Uniformidad: {zoneData.midsSpecific.colorUniformity}
            </Text>
            <Text style={styles.specificItem}>
              Oxidación: {zoneData.midsSpecific.oxidationLevel}
            </Text>
            {zoneData.midsSpecific.previousTreatments.length > 0 && (
              <Text style={styles.specificItem}>
                Tratamientos previos: {zoneData.midsSpecific.previousTreatments.join(', ')}
              </Text>
            )}
          </View>
        </View>
      )}

      {zoneData.zone === 'ends' && zoneData.endsSpecific && (
        <View style={styles.detailSection}>
          <Text style={styles.sectionTitle}>Análisis de Puntas</Text>
          <View style={styles.specificInfo}>
            <Text style={styles.specificItem}>
              Daño mecánico: {zoneData.endsSpecific.mechanicalDamage}
            </Text>
            <Text style={styles.specificItem}>
              Puntas abiertas: {zoneData.endsSpecific.splitEnds}
            </Text>
            {zoneData.endsSpecific.needsTrimming && (
              <Text style={[styles.specificItem, { color: COLORS.warning }]}>
                ⚠️ Requiere corte de puntas
              </Text>
            )}
          </View>
        </View>
      )}
    </ScrollView>
  );

  const selectedZoneData = getZoneData(selectedZone);

  return (
    <View style={styles.container}>
      <ZoneSelector />
      {selectedZoneData ? (
        <ZoneDetails zoneData={selectedZoneData} />
      ) : (
        <View style={styles.noDataContainer}>
          <MaterialCommunityIcons name="information-outline" size={48} color={COLORS.gray400} />
          <Text style={styles.noDataText}>
            No hay datos de análisis para {getZoneName(selectedZone)}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  zoneSelector: {
    flexDirection: 'row',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    gap: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  zoneButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray50,
    position: 'relative',
  },
  zoneButtonSelected: {
    backgroundColor: COLORS.primary,
  },
  zoneButtonDisabled: {
    opacity: 0.5,
  },
  zoneButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  zoneButtonTextSelected: {
    color: COLORS.textInverse,
  },
  confidenceIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  zoneDetails: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  detailSection: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  parameterCard: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  parameterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  parameterTitle: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  confidenceBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  confidenceText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  parameterValue: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  parameterDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  technicalGrid: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  technicalItem: {
    flex: 1,
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    alignItems: 'center',
  },
  technicalLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  technicalValue: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  technicalSubtext: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textTertiary,
  },
  conditionGrid: {
    gap: SPACING.sm,
  },
  conditionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  conditionLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  conditionIndicator: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  conditionText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.warning + '10',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginTop: SPACING.md,
    gap: SPACING.sm,
  },
  warningContent: {
    flex: 1,
  },
  warningTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  warningText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.warning,
  },
  specificInfo: {
    gap: SPACING.sm,
  },
  specificItem: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    paddingVertical: SPACING.xs,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray100,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  noDataText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.md,
  },
});

export default ZoneAnalysisDisplay;
