// Constantes principales de Salonier

// Colores del tema
export const COLORS = {
  primary: '#8B5CF6', // Violeta elegante para belleza
  primaryDark: '#7C3AED',
  primaryLight: '#A78BFA',
  
  secondary: '#EC4899', // Rosa vibrante
  secondaryDark: '#DB2777',
  secondaryLight: '#F472B6',
  
  accent: '#F59E0B', // Dorado para elementos premium
  accentDark: '#D97706',
  accentLight: '#FCD34D',
  
  // Grises
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // Estados
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Fondos
  background: '#FFFFFF',
  backgroundSecondary: '#F9FAFB',
  surface: '#FFFFFF',
  
  // Texto
  textPrimary: '#111827',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',
} as const;

// Espaciado
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

// Tipografía
export const TYPOGRAPHY = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  weights: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
} as const;

// Bordes y sombras
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
} as const;

// Niveles de cabello
export const HAIR_LEVELS = [
  { level: 1, name: 'Negro', color: '#1a1a1a' },
  { level: 2, name: 'Castaño muy oscuro', color: '#2d1b0e' },
  { level: 3, name: 'Castaño oscuro', color: '#3d2914' },
  { level: 4, name: 'Castaño medio', color: '#5d3317' },
  { level: 5, name: 'Castaño claro', color: '#7d4a1a' },
  { level: 6, name: 'Rubio oscuro', color: '#a0651e' },
  { level: 7, name: 'Rubio medio', color: '#c4822a' },
  { level: 8, name: 'Rubio claro', color: '#d4a574' },
  { level: 9, name: 'Rubio muy claro', color: '#e6c8a0' },
  { level: 10, name: 'Rubio platino', color: '#f5e6cc' },
] as const;

// Subtonos
export const UNDERTONES = [
  { key: 'warm', name: 'Cálido', description: 'Tonos dorados, cobrizos' },
  { key: 'cool', name: 'Frío', description: 'Tonos cenizos, violetas' },
  { key: 'neutral', name: 'Neutro', description: 'Balance entre cálido y frío' },
] as const;

// Técnicas de coloración
export const COLOR_TECHNIQUES = [
  { key: 'all-over-color', name: 'Color completo', icon: 'palette' },
  { key: 'highlights', name: 'Mechas', icon: 'brush' },
  { key: 'lowlights', name: 'Lowlights', icon: 'brush-variant' },
  { key: 'balayage', name: 'Balayage', icon: 'gesture-swipe' },
  { key: 'ombre', name: 'Ombré', icon: 'gradient-vertical' },
  { key: 'color-correction', name: 'Corrección de color', icon: 'auto-fix' },
  { key: 'fantasy-color', name: 'Color fantasía', icon: 'star' },
] as const;

// Estados de citas
export const APPOINTMENT_STATUSES = [
  { key: 'scheduled', name: 'Programada', color: COLORS.info },
  { key: 'confirmed', name: 'Confirmada', color: COLORS.success },
  { key: 'in-progress', name: 'En proceso', color: COLORS.warning },
  { key: 'completed', name: 'Completada', color: COLORS.success },
  { key: 'cancelled', name: 'Cancelada', color: COLORS.error },
  { key: 'no-show', name: 'No asistió', color: COLORS.error },
] as const;

// Categorías de productos
export const PRODUCT_CATEGORIES = [
  { key: 'color', name: 'Tintes', icon: 'palette' },
  { key: 'developer', name: 'Reveladores', icon: 'flask' },
  { key: 'bleach', name: 'Decolorantes', icon: 'lightbulb' },
  { key: 'treatment', name: 'Tratamientos', icon: 'heart' },
  { key: 'styling', name: 'Peinado', icon: 'hair-dryer' },
  { key: 'tools', name: 'Herramientas', icon: 'tools' },
  { key: 'retail', name: 'Venta', icon: 'shopping' },
] as const;

// Roles de usuario
export const USER_ROLES = [
  { key: 'admin', name: 'Administrador', permissions: ['all'] },
  { key: 'stylist', name: 'Estilista', permissions: ['consultation', 'clients', 'appointments'] },
  { key: 'receptionist', name: 'Recepcionista', permissions: ['appointments', 'clients'] },
] as const;

// Configuración de la app
export const APP_CONFIG = {
  name: 'Salonier',
  version: '1.0.0',
  supportEmail: '<EMAIL>',
  privacyPolicyUrl: 'https://salonier.com/privacy',
  termsOfServiceUrl: 'https://salonier.com/terms',
  maxImageSize: 5 * 1024 * 1024, // 5MB
  maxImagesPerConsultation: 10,
  defaultAppointmentDuration: 120, // minutos
  reminderHours: [24, 2], // horas antes de la cita
} as const;

// Configuración de navegación
export const NAVIGATION_CONFIG = {
  headerHeight: 60,
  tabBarHeight: 80,
  animationDuration: 300,
} as const;

// Mensajes de error comunes
export const ERROR_MESSAGES = {
  network: 'Error de conexión. Verifica tu internet.',
  unauthorized: 'No tienes permisos para esta acción.',
  notFound: 'Recurso no encontrado.',
  validation: 'Por favor verifica los datos ingresados.',
  generic: 'Ha ocurrido un error inesperado.',
  imageUpload: 'Error al subir la imagen.',
  cameraPermission: 'Se necesita permiso para usar la cámara.',
  storagePermission: 'Se necesita permiso para acceder al almacenamiento.',
} as const;

// Configuración de validación
export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  name: {
    minLength: 2,
    maxLength: 50,
  },
} as const;

// Configuración de formato
export const FORMAT_CONFIG = {
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm',
  dateTimeFormat: 'DD/MM/YYYY HH:mm',
  currency: 'EUR',
  currencySymbol: '€',
  locale: 'es-ES',
} as const;

// Configuración de almacenamiento local
export const STORAGE_KEYS = {
  user: 'user',
  token: 'auth_token',
  preferences: 'user_preferences',
  onboardingCompleted: 'onboarding_completed',
  lastSync: 'last_sync',
  offlineData: 'offline_data',
} as const;

// Configuración de notificaciones
export const NOTIFICATION_TYPES = {
  appointment: 'appointment',
  client: 'client',
  inventory: 'inventory',
  marketing: 'marketing',
  system: 'system',
} as const;

// Configuración de análisis
export const ANALYTICS_EVENTS = {
  consultationStarted: 'consultation_started',
  consultationCompleted: 'consultation_completed',
  clientCreated: 'client_created',
  appointmentScheduled: 'appointment_scheduled',
  productUsed: 'product_used',
  formulaGenerated: 'formula_generated',
} as const;
