import { useAuth } from '../contexts/AuthContext';
import { User } from '../types';

// Definición de permisos granulares
export type Permission = 
  // Consultas
  | 'consultation.create'
  | 'consultation.view'
  | 'consultation.edit'
  | 'consultation.delete'
  | 'consultation.view_all'
  
  // Clientes
  | 'clients.create'
  | 'clients.view'
  | 'clients.edit'
  | 'clients.delete'
  | 'clients.view_all'
  | 'clients.export'
  
  // Citas
  | 'appointments.create'
  | 'appointments.view'
  | 'appointments.edit'
  | 'appointments.delete'
  | 'appointments.view_all'
  | 'appointments.manage_others'
  
  // Inventario
  | 'inventory.view'
  | 'inventory.edit'
  | 'inventory.create'
  | 'inventory.delete'
  | 'inventory.reports'
  
  // Analíticas
  | 'analytics.view_basic'
  | 'analytics.view_advanced'
  | 'analytics.view_financial'
  | 'analytics.export'
  
  // Configuración
  | 'settings.view'
  | 'settings.edit'
  | 'settings.manage_users'
  | 'settings.manage_salon'
  
  // Administración
  | 'admin.full_access'
  | 'admin.user_management'
  | 'admin.system_settings'
  | 'admin.data_export';

// Permisos por rol
const ROLE_PERMISSIONS: Record<User['role'], Permission[]> = {
  admin: [
    // Acceso completo a todo
    'admin.full_access',
    'admin.user_management',
    'admin.system_settings',
    'admin.data_export',
    
    // Consultas
    'consultation.create',
    'consultation.view',
    'consultation.edit',
    'consultation.delete',
    'consultation.view_all',
    
    // Clientes
    'clients.create',
    'clients.view',
    'clients.edit',
    'clients.delete',
    'clients.view_all',
    'clients.export',
    
    // Citas
    'appointments.create',
    'appointments.view',
    'appointments.edit',
    'appointments.delete',
    'appointments.view_all',
    'appointments.manage_others',
    
    // Inventario
    'inventory.view',
    'inventory.edit',
    'inventory.create',
    'inventory.delete',
    'inventory.reports',
    
    // Analíticas
    'analytics.view_basic',
    'analytics.view_advanced',
    'analytics.view_financial',
    'analytics.export',
    
    // Configuración
    'settings.view',
    'settings.edit',
    'settings.manage_users',
    'settings.manage_salon',
  ],
  
  stylist: [
    // Consultas - acceso completo
    'consultation.create',
    'consultation.view',
    'consultation.edit',
    'consultation.delete',
    
    // Clientes - acceso completo
    'clients.create',
    'clients.view',
    'clients.edit',
    'clients.view_all',
    
    // Citas - solo sus propias citas
    'appointments.create',
    'appointments.view',
    'appointments.edit',
    'appointments.delete',
    
    // Inventario - solo visualización y uso
    'inventory.view',
    'inventory.edit', // Para registrar uso de productos
    
    // Analíticas básicas
    'analytics.view_basic',
    
    // Configuración personal
    'settings.view',
    'settings.edit',
  ],
  
  receptionist: [
    // Clientes - gestión básica
    'clients.create',
    'clients.view',
    'clients.edit',
    'clients.view_all',
    
    // Citas - gestión completa
    'appointments.create',
    'appointments.view',
    'appointments.edit',
    'appointments.delete',
    'appointments.view_all',
    'appointments.manage_others',
    
    // Inventario - solo visualización
    'inventory.view',
    
    // Configuración personal
    'settings.view',
    'settings.edit',
  ],
};

// Módulos y sus permisos requeridos
const MODULE_PERMISSIONS: Record<string, Permission[]> = {
  dashboard: ['consultation.view', 'appointments.view', 'clients.view'],
  consultation: ['consultation.create', 'consultation.view'],
  clients: ['clients.view'],
  appointments: ['appointments.view'],
  inventory: ['inventory.view'],
  analytics: ['analytics.view_basic'],
  settings: ['settings.view'],
  admin: ['admin.full_access'],
};

export const usePermissions = () => {
  const { user } = useAuth();

  // Verificar si el usuario tiene un permiso específico
  const hasPermission = (permission: Permission): boolean => {
    if (!user) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    
    // Admin tiene acceso a todo
    if (userPermissions.includes('admin.full_access')) {
      return true;
    }
    
    return userPermissions.includes(permission);
  };

  // Verificar si el usuario tiene alguno de los permisos especificados
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  // Verificar si el usuario tiene todos los permisos especificados
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  // Verificar acceso a un módulo
  const canAccessModule = (module: string): boolean => {
    const requiredPermissions = MODULE_PERMISSIONS[module];
    if (!requiredPermissions) return false;
    
    return hasAnyPermission(requiredPermissions);
  };

  // Verificar si puede gestionar otros usuarios (para citas, etc.)
  const canManageOthers = (): boolean => {
    return hasPermission('appointments.manage_others') || 
           hasPermission('admin.full_access');
  };

  // Verificar si puede ver datos financieros
  const canViewFinancials = (): boolean => {
    return hasPermission('analytics.view_financial') || 
           hasPermission('admin.full_access');
  };

  // Verificar si puede exportar datos
  const canExportData = (): boolean => {
    return hasPermission('clients.export') || 
           hasPermission('analytics.export') || 
           hasPermission('admin.data_export');
  };

  // Verificar si puede gestionar inventario
  const canManageInventory = (): boolean => {
    return hasPermission('inventory.edit') || 
           hasPermission('inventory.create') || 
           hasPermission('inventory.delete');
  };

  // Verificar si puede gestionar configuración del salón
  const canManageSalon = (): boolean => {
    return hasPermission('settings.manage_salon') || 
           hasPermission('admin.full_access');
  };

  // Verificar si puede gestionar usuarios
  const canManageUsers = (): boolean => {
    return hasPermission('settings.manage_users') || 
           hasPermission('admin.user_management') || 
           hasPermission('admin.full_access');
  };

  // Obtener todos los permisos del usuario
  const getUserPermissions = (): Permission[] => {
    if (!user) return [];
    return ROLE_PERMISSIONS[user.role] || [];
  };

  // Verificaciones de rol
  const isAdmin = user?.role === 'admin';
  const isStylist = user?.role === 'stylist';
  const isReceptionist = user?.role === 'receptionist';

  // Verificar si es el propietario de un recurso (para consultas, citas propias, etc.)
  const isResourceOwner = (resourceUserId: string): boolean => {
    return user?.id === resourceUserId;
  };

  // Verificar acceso a un recurso específico
  const canAccessResource = (
    permission: Permission, 
    resourceUserId?: string,
    requireOwnership: boolean = false
  ): boolean => {
    if (!hasPermission(permission)) return false;
    
    if (requireOwnership && resourceUserId) {
      return isResourceOwner(resourceUserId) || hasPermission('admin.full_access');
    }
    
    return true;
  };

  return {
    // Verificaciones básicas
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessModule,
    
    // Verificaciones específicas
    canManageOthers,
    canViewFinancials,
    canExportData,
    canManageInventory,
    canManageSalon,
    canManageUsers,
    
    // Información del usuario
    getUserPermissions,
    isAdmin,
    isStylist,
    isReceptionist,
    
    // Verificaciones de recursos
    isResourceOwner,
    canAccessResource,
    
    // Datos útiles
    userRole: user?.role,
    userId: user?.id,
  };
};
