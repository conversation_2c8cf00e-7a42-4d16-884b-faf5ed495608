// Tipos principales para Salonier
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  profileImage?: string;
  phone?: string;
  salonId?: string;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'stylist' | 'receptionist';

export interface UserPreferences {
  favoriteProducts: string[];
  measurementUnits: 'metric' | 'imperial';
  notifications: NotificationSettings;
  language: 'es' | 'en';
  theme: 'light' | 'dark' | 'auto';
}

export interface NotificationSettings {
  appointments: boolean;
  clients: boolean;
  inventory: boolean;
  marketing: boolean;
}

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  birthday?: string;
  profileImage?: string;
  allergies: Allergy[];
  preferences: ClientPreferences;
  history: ConsultationHistory[];
  createdAt: string;
  updatedAt: string;
}

export interface Allergy {
  id: string;
  substance: string;
  severity: 'mild' | 'moderate' | 'severe';
  notes?: string;
  testedDate?: string;
}

export interface ClientPreferences {
  preferredStylist?: string;
  communicationMethod: 'email' | 'phone' | 'whatsapp';
  reminderTime: number; // horas antes de la cita
}

export interface ConsultationHistory {
  id: string;
  date: string;
  stylistId: string;
  diagnosis: HairDiagnosis;
  targetColor: ColorAnalysis;
  formula: ColorFormula;
  result: ConsultationResult;
  photos: ConsultationPhotos;
  notes: string;
  satisfaction: number; // 1-5
  cost: number;
}

export interface HairDiagnosis {
  naturalLevel: number; // 1-10
  undertone: 'warm' | 'cool' | 'neutral';
  grayPercentage: number;
  diameter: 'fine' | 'medium' | 'coarse';
  density: 'low' | 'medium' | 'high';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'poor' | 'normal' | 'excellent';
  resistance: 'low' | 'medium' | 'high';
  existingColor?: string;
  previousTreatments: ChemicalTreatment[];
}

export interface ChemicalTreatment {
  id: string;
  type: 'color' | 'bleach' | 'perm' | 'relaxer' | 'keratin';
  date: string;
  products: string[];
  notes?: string;
}

export interface ColorAnalysis {
  targetLevel: number;
  targetTone: string;
  technique: ColorTechnique;
  referenceImages: string[];
  notes?: string;
}

export type ColorTechnique = 
  | 'all-over-color'
  | 'highlights'
  | 'lowlights'
  | 'balayage'
  | 'ombre'
  | 'color-correction'
  | 'fantasy-color';

export interface ColorFormula {
  id: string;
  products: FormulaProduct[];
  mixingRatio: string;
  processingTime: number; // minutos
  applicationMethod: string;
  steps: FormulaStep[];
  totalCost: number;
  estimatedResult: string;
}

export interface FormulaProduct {
  id: string;
  brand: string;
  line: string;
  shade: string;
  volume: number;
  unit: 'ml' | 'g';
  cost: number;
}

export interface FormulaStep {
  order: number;
  instruction: string;
  duration?: number;
  temperature?: number;
  notes?: string;
}

export interface ConsultationResult {
  achievedColor: string;
  processingTime: number;
  clientSatisfaction: number;
  stylistNotes: string;
  recommendedMaintenance: MaintenanceRecommendation;
}

export interface MaintenanceRecommendation {
  nextAppointment: string; // fecha sugerida
  homeCareTips: string[];
  recommendedProducts: string[];
}

export interface ConsultationPhotos {
  before: string[];
  after: string[];
  process?: string[];
}

export interface Appointment {
  id: string;
  clientId: string;
  stylistId: string;
  serviceId: string;
  date: string;
  duration: number; // minutos
  status: AppointmentStatus;
  notes?: string;
  estimatedCost: number;
  actualCost?: number;
  createdAt: string;
  updatedAt: string;
}

export type AppointmentStatus = 
  | 'scheduled'
  | 'confirmed'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'no-show';

export interface Service {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  baseDuration: number; // minutos
  category: ServiceCategory;
  requiredProducts: string[];
  priceModifiers: PriceModifier[];
  isActive: boolean;
}

export type ServiceCategory = 
  | 'color'
  | 'cut'
  | 'treatment'
  | 'styling'
  | 'consultation';

export interface PriceModifier {
  condition: string; // ej: "hair_length > 20cm"
  multiplier: number;
  description: string;
}

export interface Product {
  id: string;
  brand: string;
  line: string;
  name: string;
  category: ProductCategory;
  size: number;
  unit: 'ml' | 'g' | 'piece';
  cost: number;
  retailPrice?: number;
  stock: number;
  minStock: number;
  barcode?: string;
  expirationDate?: string;
  supplier?: string;
  isActive: boolean;
}

export type ProductCategory = 
  | 'color'
  | 'developer'
  | 'bleach'
  | 'treatment'
  | 'styling'
  | 'tools'
  | 'retail';

export interface InventoryTransaction {
  id: string;
  productId: string;
  type: 'purchase' | 'usage' | 'waste' | 'adjustment';
  quantity: number;
  cost?: number;
  reason?: string;
  userId: string;
  date: string;
}

// Tipos para navegación
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Consultation: { clientId?: string };
  ClientProfile: { clientId: string };
  AppointmentDetails: { appointmentId: string };
  ProductDetails: { productId: string };
  Settings: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Clients: undefined;
  Appointments: undefined;
  Inventory: undefined;
  Analytics: undefined;
  Profile: undefined;
};

// Tipos para formularios
export interface ConsultationFormData {
  clientId: string;
  diagnosis: Partial<HairDiagnosis>;
  targetColor: Partial<ColorAnalysis>;
  photos: {
    before: string[];
    reference: string[];
  };
}

// Tipos para el flujo de consulta
export interface ConsultationFlow {
  id: string;
  clientId: string;
  stylistId: string;
  currentStep: ConsultationStep;
  startedAt: string;
  completedAt?: string;
  data: ConsultationFlowData;
}

export type ConsultationStep =
  | 'client-selection'
  | 'safety-protocol'
  | 'image-capture'
  | 'ai-analysis'
  | 'ai-validation'
  | 'color-target'
  | 'formula-generation'
  | 'documentation'
  | 'completed';

export interface ConsultationFlowData {
  clientSelection?: ClientSelectionData;
  safetyProtocol?: SafetyProtocolData;
  imageCapture?: ImageCaptureData;
  aiAnalysis?: AIAnalysisData;
  colorTarget?: ColorTargetData;
  formulaGeneration?: FormulaGenerationData;
  documentation?: DocumentationData;
}

export interface ClientSelectionData {
  selectedClientId: string;
  isGuestClient: boolean;
  guestClientInfo?: {
    name: string;
    phone?: string;
    email?: string;
  };
}

export interface SafetyProtocolData {
  consentObtained: boolean;
  consentSignature?: string;
  consentDate: string;
  patchTestRequired: boolean;
  patchTestCompleted?: boolean;
  patchTestDate?: string;
  patchTestResult?: 'positive' | 'negative' | 'pending';
  allergiesChecked: boolean;
  knownAllergies: string[];
  safetyChecklist: SafetyChecklistItem[];
}

export interface SafetyChecklistItem {
  id: string;
  description: string;
  completed: boolean;
  required: boolean;
  notes?: string;
}

export interface ImageCaptureData {
  capturedImages: CapturedImage[];
  qualityChecks: ImageQualityCheck[];
  captureNotes?: string;
}

export interface CapturedImage {
  id: string;
  uri: string;
  angle: ImageAngle;
  timestamp: string;
  qualityScore: number;
  metadata: ImageMetadata;
}

export type ImageAngle =
  | 'front'
  | 'back'
  | 'left-side'
  | 'right-side'
  | 'top'
  | 'close-up-roots'
  | 'close-up-ends';

export interface ImageMetadata {
  width: number;
  height: number;
  lighting: 'poor' | 'fair' | 'good' | 'excellent';
  focus: 'poor' | 'fair' | 'good' | 'excellent';
  angle: 'poor' | 'fair' | 'good' | 'excellent';
}

export interface ImageQualityCheck {
  imageId: string;
  overallScore: number;
  lightingScore: number;
  focusScore: number;
  angleScore: number;
  recommendations: string[];
  approved: boolean;
}

export interface AIAnalysisData {
  analysisId: string;
  processedAt: string;
  processingTime: number; // milliseconds
  confidence: number; // 0-1
  results: AIAnalysisResults;
  recommendations: AIRecommendation[];
  stylistValidation?: StylistValidation;
}

// Validación del estilista sobre los resultados de IA
export interface StylistValidation {
  validatedAt: string;
  stylistId: string;
  overallApproval: boolean;
  zoneValidations: ZoneValidation[];
  manualAdjustments: ManualAdjustment[];
  additionalNotes: string;
  confidenceRating: number; // 1-5, qué tan confiable considera el análisis
}

export interface ZoneValidation {
  zone: 'roots' | 'mids' | 'ends';
  approved: boolean;
  corrections: {
    parameter: string;
    aiValue: any;
    correctedValue: any;
    reason: string;
  }[];
  additionalObservations: string;
}

export interface ManualAdjustment {
  parameter: string;
  originalValue: any;
  adjustedValue: any;
  reason: string;
  confidence: 'low' | 'medium' | 'high';
}

// Análisis por zonas del cabello
export interface HairZoneAnalysis {
  zone: 'roots' | 'mids' | 'ends';
  naturalLevel: {
    value: number; // 1-10
    confidence: number;
    description: string;
  };
  artificialColor: {
    detected: boolean;
    level?: number;
    tone?: string;
    brand?: string;
    ageWeeks?: number; // Semanas desde aplicación
    fadeLevel?: 'none' | 'minimal' | 'moderate' | 'significant';
  };
  undertone: {
    value: 'warm' | 'cool' | 'neutral';
    confidence: number;
    description: string;
  };
  porosity: {
    value: 'low' | 'medium' | 'high';
    confidence: number;
    description: string;
    uniformity: 'even' | 'uneven';
  };
  elasticity: {
    value: 'poor' | 'normal' | 'excellent';
    confidence: number;
    stretchTest: boolean; // Si se realizó test físico
  };
  condition: {
    damage: 'none' | 'minimal' | 'moderate' | 'severe';
    dryness: 'none' | 'mild' | 'moderate' | 'severe';
    breakage: 'none' | 'minimal' | 'moderate' | 'severe';
    chemicalResidues: boolean;
    metalDeposits: boolean;
  };
  // Específico por zona
  rootsSpecific?: {
    growthLength: number; // mm de crecimiento
    grayPercentage: number;
    grayDistribution: 'even' | 'concentrated' | 'scattered';
    naturalTexture: 'straight' | 'wavy' | 'curly' | 'coily';
  };
  midsSpecific?: {
    colorUniformity: 'even' | 'banded' | 'patchy';
    previousTreatments: string[];
    oxidationLevel: 'none' | 'minimal' | 'moderate' | 'severe';
  };
  endsSpecific?: {
    mechanicalDamage: 'none' | 'minimal' | 'moderate' | 'severe';
    splitEnds: 'none' | 'minimal' | 'moderate' | 'severe';
    porosityVariation: boolean;
    needsTrimming: boolean;
  };
}

export interface AIAnalysisResults {
  // Análisis general
  overallAssessment: {
    hairType: 'virgin' | 'color-treated' | 'chemically-processed' | 'damaged';
    complexity: 'simple' | 'moderate' | 'complex' | 'high-risk';
    recommendedApproach: string;
  };

  // Análisis por zonas
  zoneAnalysis: HairZoneAnalysis[];

  // Características generales
  diameter: {
    value: 'fine' | 'medium' | 'coarse';
    confidence: number;
    description: string;
  };
  density: {
    value: 'low' | 'medium' | 'high';
    confidence: number;
    description: string;
  };

  // Análisis de uniformidad entre zonas
  uniformityAnalysis: {
    levelVariation: number; // Diferencia máxima entre zonas
    toneVariation: boolean;
    porosityVariation: boolean;
    recommendations: string[];
  };

  // Historial químico detectado
  chemicalHistory: {
    previousColor: boolean;
    bleachHistory: boolean;
    permanentWave: boolean;
    relaxer: boolean;
    estimatedLastService: string; // "2-4 weeks ago"
    compatibilityWarnings: string[];
  };
}

export interface AIRecommendation {
  id: string;
  type: 'safety' | 'technique' | 'product' | 'timing' | 'aftercare';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  reasoning: string;
  actionRequired?: boolean;
}

export interface ColorTargetData {
  targetImages: string[];
  targetDescription: string;
  targetLevel?: number;
  targetTone?: string;
  technique: ColorTechnique;
  inspiration: string[];
  clientExpectations: string;
  timelineExpected: string;
  budgetRange?: {
    min: number;
    max: number;
  };
}

export interface FormulaGenerationData {
  generatedFormulas: GeneratedFormula[];
  selectedFormulaId?: string;
  customizations: FormulaCustomization[];
  costBreakdown: CostBreakdown;
}

export interface GeneratedFormula {
  id: string;
  name: string;
  confidence: number;
  estimatedResult: string;
  processingTime: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  products: FormulaProduct[];
  steps: FormulaStep[];
  warnings: string[];
  alternatives: AlternativeFormula[];
}

export interface AlternativeFormula {
  id: string;
  reason: string;
  products: FormulaProduct[];
  adjustments: string[];
}

export interface FormulaCustomization {
  type: 'product-substitution' | 'timing-adjustment' | 'technique-modification';
  description: string;
  impact: string;
  applied: boolean;
}

export interface CostBreakdown {
  materials: number;
  labor: number;
  overhead: number;
  total: number;
  profitMargin: number;
  suggestedPrice: number;
  priceRange: {
    min: number;
    max: number;
  };
}

export interface DocumentationData {
  serviceNotes: string;
  clientFeedback: string;
  stylistNotes: string;
  beforePhotos: string[];
  afterPhotos: string[];
  processPhotos: string[];
  satisfactionRating: number; // 1-5
  followUpRequired: boolean;
  followUpDate?: string;
  homeCareTips: string[];
  nextAppointmentSuggested?: string;
}

// Tipos para API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
