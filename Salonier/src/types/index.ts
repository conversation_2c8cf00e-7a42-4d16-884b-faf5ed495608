// Tipos principales para Salonier
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  profileImage?: string;
  phone?: string;
  salonId?: string;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'stylist' | 'receptionist';

export interface UserPreferences {
  favoriteProducts: string[];
  measurementUnits: 'metric' | 'imperial';
  notifications: NotificationSettings;
  language: 'es' | 'en';
  theme: 'light' | 'dark' | 'auto';
}

export interface NotificationSettings {
  appointments: boolean;
  clients: boolean;
  inventory: boolean;
  marketing: boolean;
}

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  birthday?: string;
  profileImage?: string;
  allergies: Allergy[];
  preferences: ClientPreferences;
  history: ConsultationHistory[];
  createdAt: string;
  updatedAt: string;
}

export interface Allergy {
  id: string;
  substance: string;
  severity: 'mild' | 'moderate' | 'severe';
  notes?: string;
  testedDate?: string;
}

export interface ClientPreferences {
  preferredStylist?: string;
  communicationMethod: 'email' | 'phone' | 'whatsapp';
  reminderTime: number; // horas antes de la cita
}

export interface ConsultationHistory {
  id: string;
  date: string;
  stylistId: string;
  diagnosis: HairDiagnosis;
  targetColor: ColorAnalysis;
  formula: ColorFormula;
  result: ConsultationResult;
  photos: ConsultationPhotos;
  notes: string;
  satisfaction: number; // 1-5
  cost: number;
}

export interface HairDiagnosis {
  naturalLevel: number; // 1-10
  undertone: 'warm' | 'cool' | 'neutral';
  grayPercentage: number;
  diameter: 'fine' | 'medium' | 'coarse';
  density: 'low' | 'medium' | 'high';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'poor' | 'normal' | 'excellent';
  resistance: 'low' | 'medium' | 'high';
  existingColor?: string;
  previousTreatments: ChemicalTreatment[];
}

export interface ChemicalTreatment {
  id: string;
  type: 'color' | 'bleach' | 'perm' | 'relaxer' | 'keratin';
  date: string;
  products: string[];
  notes?: string;
}

export interface ColorAnalysis {
  targetLevel: number;
  targetTone: string;
  technique: ColorTechnique;
  referenceImages: string[];
  notes?: string;
}

export type ColorTechnique = 
  | 'all-over-color'
  | 'highlights'
  | 'lowlights'
  | 'balayage'
  | 'ombre'
  | 'color-correction'
  | 'fantasy-color';

export interface ColorFormula {
  id: string;
  products: FormulaProduct[];
  mixingRatio: string;
  processingTime: number; // minutos
  applicationMethod: string;
  steps: FormulaStep[];
  totalCost: number;
  estimatedResult: string;
}

export interface FormulaProduct {
  id: string;
  brand: string;
  line: string;
  shade: string;
  volume: number;
  unit: 'ml' | 'g';
  cost: number;
}

export interface FormulaStep {
  order: number;
  instruction: string;
  duration?: number;
  temperature?: number;
  notes?: string;
}

export interface ConsultationResult {
  achievedColor: string;
  processingTime: number;
  clientSatisfaction: number;
  stylistNotes: string;
  recommendedMaintenance: MaintenanceRecommendation;
}

export interface MaintenanceRecommendation {
  nextAppointment: string; // fecha sugerida
  homeCareTips: string[];
  recommendedProducts: string[];
}

export interface ConsultationPhotos {
  before: string[];
  after: string[];
  process?: string[];
}

export interface Appointment {
  id: string;
  clientId: string;
  stylistId: string;
  serviceId: string;
  date: string;
  duration: number; // minutos
  status: AppointmentStatus;
  notes?: string;
  estimatedCost: number;
  actualCost?: number;
  createdAt: string;
  updatedAt: string;
}

export type AppointmentStatus = 
  | 'scheduled'
  | 'confirmed'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'no-show';

export interface Service {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  baseDuration: number; // minutos
  category: ServiceCategory;
  requiredProducts: string[];
  priceModifiers: PriceModifier[];
  isActive: boolean;
}

export type ServiceCategory = 
  | 'color'
  | 'cut'
  | 'treatment'
  | 'styling'
  | 'consultation';

export interface PriceModifier {
  condition: string; // ej: "hair_length > 20cm"
  multiplier: number;
  description: string;
}

export interface Product {
  id: string;
  brand: string;
  line: string;
  name: string;
  category: ProductCategory;
  size: number;
  unit: 'ml' | 'g' | 'piece';
  cost: number;
  retailPrice?: number;
  stock: number;
  minStock: number;
  barcode?: string;
  expirationDate?: string;
  supplier?: string;
  isActive: boolean;
}

export type ProductCategory = 
  | 'color'
  | 'developer'
  | 'bleach'
  | 'treatment'
  | 'styling'
  | 'tools'
  | 'retail';

export interface InventoryTransaction {
  id: string;
  productId: string;
  type: 'purchase' | 'usage' | 'waste' | 'adjustment';
  quantity: number;
  cost?: number;
  reason?: string;
  userId: string;
  date: string;
}

// Tipos para navegación
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Consultation: { clientId?: string };
  ClientProfile: { clientId: string };
  AppointmentDetails: { appointmentId: string };
  ProductDetails: { productId: string };
  Settings: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Clients: undefined;
  Appointments: undefined;
  Inventory: undefined;
  Analytics: undefined;
  Profile: undefined;
};

// Tipos para formularios
export interface ConsultationFormData {
  clientId: string;
  diagnosis: Partial<HairDiagnosis>;
  targetColor: Partial<ColorAnalysis>;
  photos: {
    before: string[];
    reference: string[];
  };
}

// Tipos para API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
