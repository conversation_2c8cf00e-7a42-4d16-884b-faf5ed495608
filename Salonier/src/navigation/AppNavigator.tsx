import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { View, ActivityIndicator } from 'react-native';
import * as SecureStore from 'expo-secure-store';

import { useAuth } from '../contexts/AuthContext';
import { RootStackParamList, MainTabParamList } from '../types';
import { COLORS, STORAGE_KEYS } from '../constants';

// Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import OnboardingScreen from '../screens/onboarding/OnboardingScreen';
import DashboardScreen from '../screens/dashboard/DashboardScreen';
import ClientsScreen from '../screens/clients/ClientsScreen';
import AppointmentsScreen from '../screens/appointments/AppointmentsScreen';
import InventoryScreen from '../screens/inventory/InventoryScreen';
import AnalyticsScreen from '../screens/analytics/AnalyticsScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import PreferencesScreen from '../screens/profile/PreferencesScreen';
import ConsultationFlowScreen from '../screens/consultation/ConsultationFlowScreen';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// Componente de carga
const LoadingScreen: React.FC = () => (
  <View style={{ 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center',
    backgroundColor: COLORS.background 
  }}>
    <ActivityIndicator size="large" color={COLORS.primary} />
  </View>
);

// Navegador de pestañas principales
const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof MaterialCommunityIcons.glyphMap;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'view-dashboard' : 'view-dashboard-outline';
              break;
            case 'Clients':
              iconName = focused ? 'account-group' : 'account-group-outline';
              break;
            case 'Appointments':
              iconName = focused ? 'calendar-clock' : 'calendar-clock-outline';
              break;
            case 'Inventory':
              iconName = focused ? 'package-variant' : 'package-variant-closed';
              break;
            case 'Analytics':
              iconName = focused ? 'chart-line' : 'chart-line-variant';
              break;
            case 'Profile':
              iconName = focused ? 'account-circle' : 'account-circle-outline';
              break;
            default:
              iconName = 'help-circle-outline';
          }

          return <MaterialCommunityIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.gray400,
        tabBarStyle: {
          backgroundColor: COLORS.background,
          borderTopColor: COLORS.gray200,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: COLORS.primary,
        },
        headerTintColor: COLORS.textInverse,
        headerTitleStyle: {
          fontWeight: '600',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ 
          title: 'Inicio',
          headerTitle: 'Salonier Dashboard'
        }}
      />
      <Tab.Screen 
        name="Clients" 
        component={ClientsScreen}
        options={{ 
          title: 'Clientes',
          headerTitle: 'Gestión de Clientes'
        }}
      />
      <Tab.Screen 
        name="Appointments" 
        component={AppointmentsScreen}
        options={{ 
          title: 'Citas',
          headerTitle: 'Calendario de Citas'
        }}
      />
      <Tab.Screen 
        name="Inventory" 
        component={InventoryScreen}
        options={{ 
          title: 'Inventario',
          headerTitle: 'Gestión de Inventario'
        }}
      />
      <Tab.Screen 
        name="Analytics" 
        component={AnalyticsScreen}
        options={{ 
          title: 'Análisis',
          headerTitle: 'Analíticas del Salón'
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ 
          title: 'Perfil',
          headerTitle: 'Mi Perfil'
        }}
      />
    </Tab.Navigator>
  );
};

// Navegador de autenticación
const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: COLORS.primary,
        },
        headerTintColor: COLORS.textInverse,
        headerTitleStyle: {
          fontWeight: '600',
        },
      }}
    >
      <Stack.Screen 
        name="Login" 
        component={LoginScreen}
        options={{ 
          title: 'Iniciar Sesión',
          headerShown: false // Ocultamos el header para la pantalla de login
        }}
      />
      <Stack.Screen 
        name="Register" 
        component={RegisterScreen}
        options={{ 
          title: 'Crear Cuenta',
          headerBackTitle: 'Volver'
        }}
      />
    </Stack.Navigator>
  );
};

// Navegador principal de la aplicación
const AppNavigator: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [checkingOnboarding, setCheckingOnboarding] = useState(true);

  // Verificar si se debe mostrar onboarding
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const onboardingCompleted = await SecureStore.getItemAsync(STORAGE_KEYS.onboardingCompleted);
        setShowOnboarding(!onboardingCompleted && isAuthenticated);
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        setShowOnboarding(false);
      } finally {
        setCheckingOnboarding(false);
      }
    };

    if (!isLoading) {
      checkOnboardingStatus();
    }
  }, [isAuthenticated, isLoading]);

  const handleOnboardingComplete = async () => {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.onboardingCompleted, 'true');
      setShowOnboarding(false);
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      setShowOnboarding(false);
    }
  };

  if (isLoading || checkingOnboarding) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? (
        showOnboarding ? (
          <OnboardingScreen onComplete={handleOnboardingComplete} />
        ) : (
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="Main" component={MainTabNavigator} />
            <Stack.Screen
              name="ConsultationFlow"
              component={ConsultationFlowScreen}
              options={{
                headerShown: false,
                gestureEnabled: false, // Prevenir swipe back accidental
              }}
            />
            <Stack.Screen
              name="EditProfile"
              component={EditProfileScreen}
              options={{
                headerShown: true,
                title: 'Editar Perfil',
                headerStyle: { backgroundColor: COLORS.primary },
                headerTintColor: COLORS.textInverse,
                headerTitleStyle: { fontWeight: '600' },
              }}
            />
            <Stack.Screen
              name="Preferences"
              component={PreferencesScreen}
              options={{
                headerShown: true,
                title: 'Preferencias',
                headerStyle: { backgroundColor: COLORS.primary },
                headerTintColor: COLORS.textInverse,
                headerTitleStyle: { fontWeight: '600' },
              }}
            />
          </Stack.Navigator>
        )
      ) : (
        <AuthNavigator />
      )}
    </NavigationContainer>
  );
};

export default AppNavigator;
