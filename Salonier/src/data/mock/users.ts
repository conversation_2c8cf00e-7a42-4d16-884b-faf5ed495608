import { User } from '../../types';

export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'admin',
    profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    phone: '+34 666 123 456',
    salonId: 'salon-1',
    preferences: {
      favoriteProducts: ['loreal', 'wella', 'schwarzkopf'],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: true,
        inventory: true,
        marketing: false,
      },
      language: 'es',
      theme: 'light',
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-06-20T15:30:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '<PERSON> <PERSON>',
    role: 'stylist',
    profileImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    phone: '+34 666 234 567',
    salonId: 'salon-1',
    preferences: {
      favoriteProducts: ['wella', 'matrix', 'redken'],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: true,
        inventory: false,
        marketing: false,
      },
      language: 'es',
      theme: 'light',
    },
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2024-06-22T11:15:00Z',
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Carlos Ruiz',
    role: 'stylist',
    profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    phone: '+34 666 345 678',
    salonId: 'salon-1',
    preferences: {
      favoriteProducts: ['schwarzkopf', 'goldwell', 'joico'],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: true,
        inventory: true,
        marketing: true,
      },
      language: 'es',
      theme: 'dark',
    },
    createdAt: '2024-02-15T14:00:00Z',
    updatedAt: '2024-06-21T16:45:00Z',
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Laura Martínez',
    role: 'receptionist',
    profileImage: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    phone: '+34 666 456 789',
    salonId: 'salon-1',
    preferences: {
      favoriteProducts: [],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: false,
        inventory: false,
        marketing: false,
      },
      language: 'es',
      theme: 'auto',
    },
    createdAt: '2024-03-01T08:30:00Z',
    updatedAt: '2024-06-20T12:00:00Z',
  },
];

// Usuario actual para desarrollo
export const currentUser = mockUsers[0]; // Admin por defecto

// Función para obtener usuario por ID
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

// Función para obtener usuarios por rol
export const getUsersByRole = (role: User['role']): User[] => {
  return mockUsers.filter(user => user.role === role);
};

// Función para autenticar usuario (mock)
export const authenticateUser = (email: string, password: string): User | null => {
  // En desarrollo, cualquier combinación válida devuelve el primer usuario
  const user = mockUsers.find(u => u.email === email);
  if (user && password.length >= 6) {
    return user;
  }
  return null;
};

// Función para registrar usuario (mock)
export const registerUser = (userData: Partial<User>): User => {
  const newUser: User = {
    id: `user-${Date.now()}`,
    email: userData.email || '',
    name: userData.name || '',
    role: userData.role || 'stylist',
    profileImage: userData.profileImage,
    phone: userData.phone,
    salonId: userData.salonId || 'salon-1',
    preferences: {
      favoriteProducts: [],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: true,
        inventory: false,
        marketing: false,
      },
      language: 'es',
      theme: 'light',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  mockUsers.push(newUser);
  return newUser;
};
