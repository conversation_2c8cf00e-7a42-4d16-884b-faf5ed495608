import { Client, ConsultationHistory } from '../../types';

export const mockClients: Client[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: 'el<PERSON>.<PERSON><PERSON><PERSON><PERSON>@email.com',
    phone: '+34 666 111 222',
    birthday: '1985-03-15',
    profileImage: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    allergies: [
      {
        id: 'allergy-1',
        substance: 'PPD (Parafenilendiamina)',
        severity: 'moderate',
        notes: 'Reacción cutánea leve en test de parche',
        testedDate: '2024-01-15',
      }
    ],
    preferences: {
      preferredStylist: '2',
      communicationMethod: 'whatsapp',
      reminderTime: 24,
    },
    history: [
      {
        id: 'consultation-1',
        date: '2024-06-15T10:00:00Z',
        stylistId: '2',
        diagnosis: {
          naturalLevel: 4,
          undertone: 'warm',
          grayPercentage: 15,
          diameter: 'medium',
          density: 'high',
          porosity: 'medium',
          elasticity: 'normal',
          resistance: 'medium',
          existingColor: 'Castaño con mechas doradas',
          previousTreatments: [
            {
              id: 'treatment-1',
              type: 'color',
              date: '2024-03-15',
              products: ['L\'Oréal Majirel 6.3', 'Oxidante 20vol'],
              notes: 'Color base castaño claro dorado',
            }
          ],
        },
        targetColor: {
          targetLevel: 7,
          targetTone: 'Rubio miel con reflejos dorados',
          technique: 'balayage',
          referenceImages: ['ref-image-1.jpg', 'ref-image-2.jpg'],
          notes: 'Cliente quiere un look natural y luminoso',
        },
        formula: {
          id: 'formula-1',
          products: [
            {
              id: 'product-1',
              brand: 'Wella',
              line: 'Koleston Perfect',
              shade: '7/3',
              volume: 60,
              unit: 'ml',
              cost: 8.50,
            },
            {
              id: 'product-2',
              brand: 'Wella',
              line: 'Welloxon Perfect',
              shade: '20vol',
              volume: 60,
              unit: 'ml',
              cost: 2.30,
            }
          ],
          mixingRatio: '1:1',
          processingTime: 35,
          applicationMethod: 'Balayage con papel de aluminio',
          steps: [
            {
              order: 1,
              instruction: 'Separar mechones en diagonal',
              duration: 10,
            },
            {
              order: 2,
              instruction: 'Aplicar mezcla con pincel desde medios a puntas',
              duration: 20,
            },
            {
              order: 3,
              instruction: 'Envolver en papel aluminio',
              duration: 5,
            },
            {
              order: 4,
              instruction: 'Procesar 35 minutos',
              duration: 35,
            }
          ],
          totalCost: 10.80,
          estimatedResult: 'Rubio miel natural con reflejos dorados',
        },
        result: {
          achievedColor: 'Rubio miel con reflejos dorados naturales',
          processingTime: 35,
          clientSatisfaction: 5,
          stylistNotes: 'Resultado excelente, cliente muy satisfecha. Color uniforme y natural.',
          recommendedMaintenance: {
            nextAppointment: '2024-08-15',
            homeCareTips: [
              'Usar champú sin sulfatos',
              'Aplicar mascarilla nutritiva 1 vez por semana',
              'Proteger del sol con productos con UV'
            ],
            recommendedProducts: [
              'Champú Wella SP Color Save',
              'Mascarilla Wella SP Luxe Oil',
              'Spray protector solar Wella EIMI'
            ],
          },
        },
        photos: {
          before: ['before-1.jpg', 'before-2.jpg'],
          after: ['after-1.jpg', 'after-2.jpg'],
          process: ['process-1.jpg'],
        },
        notes: 'Cliente muy colaborativa. Excelente resultado en primera sesión.',
        satisfaction: 5,
        cost: 85.00,
      }
    ],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-06-15T12:30:00Z',
  },
  {
    id: '2',
    name: 'Carmen Jiménez',
    email: '<EMAIL>',
    phone: '+34 666 333 444',
    birthday: '1978-07-22',
    profileImage: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face',
    allergies: [],
    preferences: {
      preferredStylist: '3',
      communicationMethod: 'email',
      reminderTime: 48,
    },
    history: [
      {
        id: 'consultation-2',
        date: '2024-06-10T14:00:00Z',
        stylistId: '3',
        diagnosis: {
          naturalLevel: 3,
          undertone: 'cool',
          grayPercentage: 40,
          diameter: 'fine',
          density: 'medium',
          porosity: 'high',
          elasticity: 'poor',
          resistance: 'low',
          existingColor: 'Castaño oscuro con canas',
          previousTreatments: [
            {
              id: 'treatment-2',
              type: 'color',
              date: '2024-04-10',
              products: ['Schwarzkopf Igora Royal 4-0'],
              notes: 'Cobertura de canas',
            }
          ],
        },
        targetColor: {
          targetLevel: 5,
          targetTone: 'Castaño claro cenizo',
          technique: 'all-over-color',
          referenceImages: ['ref-image-3.jpg'],
          notes: 'Cobertura total de canas con tono cenizo elegante',
        },
        formula: {
          id: 'formula-2',
          products: [
            {
              id: 'product-3',
              brand: 'Schwarzkopf',
              line: 'Igora Royal',
              shade: '5-1',
              volume: 60,
              unit: 'ml',
              cost: 9.20,
            },
            {
              id: 'product-4',
              brand: 'Schwarzkopf',
              line: 'Igora Royal',
              shade: 'Oil Developer 6%',
              volume: 60,
              unit: 'ml',
              cost: 2.80,
            }
          ],
          mixingRatio: '1:1',
          processingTime: 45,
          applicationMethod: 'Aplicación global desde raíz',
          steps: [
            {
              order: 1,
              instruction: 'Aplicar en raíces primero',
              duration: 15,
            },
            {
              order: 2,
              instruction: 'Extender a medios y puntas',
              duration: 10,
            },
            {
              order: 3,
              instruction: 'Procesar 45 minutos',
              duration: 45,
            }
          ],
          totalCost: 12.00,
          estimatedResult: 'Castaño claro cenizo con cobertura total',
        },
        result: {
          achievedColor: 'Castaño claro cenizo uniforme',
          processingTime: 45,
          clientSatisfaction: 4,
          stylistNotes: 'Buena cobertura de canas. Cliente satisfecha con el tono.',
          recommendedMaintenance: {
            nextAppointment: '2024-08-10',
            homeCareTips: [
              'Usar champú específico para canas',
              'Evitar agua muy caliente',
              'Aplicar tratamiento hidratante semanal'
            ],
            recommendedProducts: [
              'Champú Schwarzkopf BC Bonacure Color Freeze',
              'Tratamiento Schwarzkopf BC Bonacure Repair Rescue'
            ],
          },
        },
        photos: {
          before: ['before-3.jpg'],
          after: ['after-3.jpg'],
        },
        notes: 'Cliente regular, muy puntual. Prefiere tonos naturales.',
        satisfaction: 4,
        cost: 65.00,
      }
    ],
    createdAt: '2024-02-05T11:00:00Z',
    updatedAt: '2024-06-10T16:30:00Z',
  },
  {
    id: '3',
    name: 'Isabella Torres',
    email: '<EMAIL>',
    phone: '+34 666 555 666',
    birthday: '1995-11-08',
    profileImage: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    allergies: [],
    preferences: {
      preferredStylist: '2',
      communicationMethod: 'whatsapp',
      reminderTime: 12,
    },
    history: [],
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z',
  },
  {
    id: '4',
    name: 'Sofía Morales',
    email: '<EMAIL>',
    phone: '+34 666 777 888',
    birthday: '1988-12-03',
    profileImage: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',
    allergies: [
      {
        id: 'allergy-2',
        substance: 'Amoníaco',
        severity: 'mild',
        notes: 'Sensibilidad leve, usar productos sin amoníaco',
        testedDate: '2024-05-20',
      }
    ],
    preferences: {
      preferredStylist: '3',
      communicationMethod: 'phone',
      reminderTime: 24,
    },
    history: [],
    createdAt: '2024-05-20T15:00:00Z',
    updatedAt: '2024-05-20T15:00:00Z',
  },
];

// Funciones helper para trabajar con clientes
export const getClientById = (id: string): Client | undefined => {
  return mockClients.find(client => client.id === id);
};

export const getClientsByName = (name: string): Client[] => {
  return mockClients.filter(client => 
    client.name.toLowerCase().includes(name.toLowerCase())
  );
};

export const getClientsByStylist = (stylistId: string): Client[] => {
  return mockClients.filter(client => 
    client.preferences.preferredStylist === stylistId
  );
};

export const addClient = (clientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Client => {
  const newClient: Client = {
    ...clientData,
    id: `client-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  mockClients.push(newClient);
  return newClient;
};

export const updateClient = (id: string, updates: Partial<Client>): Client | null => {
  const clientIndex = mockClients.findIndex(client => client.id === id);
  if (clientIndex === -1) return null;
  
  mockClients[clientIndex] = {
    ...mockClients[clientIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  return mockClients[clientIndex];
};
