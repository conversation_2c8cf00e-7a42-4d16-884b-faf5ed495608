import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { mockClients } from '../../data/mock/clients';
import { Client } from '../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

const ClientsScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [clients, setClients] = useState(mockClients);

  // Filtrar clientes por búsqueda
  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.phone?.includes(searchQuery)
  );

  const handleAddClient = () => {
    console.log('Agregar nuevo cliente');
  };

  const handleClientPress = (client: Client) => {
    console.log('Ver perfil de cliente:', client.name);
  };

  const ClientCard: React.FC<{ client: Client }> = ({ client }) => {
    const lastVisit = client.history.length > 0 
      ? new Date(client.history[client.history.length - 1].date).toLocaleDateString('es-ES')
      : 'Sin visitas';

    const totalVisits = client.history.length;
    const hasAllergies = client.allergies.length > 0;

    return (
      <TouchableOpacity 
        style={styles.clientCard}
        onPress={() => handleClientPress(client)}
      >
        <View style={styles.clientHeader}>
          <View style={styles.clientImageContainer}>
            {client.profileImage ? (
              <Image source={{ uri: client.profileImage }} style={styles.clientImage} />
            ) : (
              <View style={styles.clientImagePlaceholder}>
                <MaterialCommunityIcons 
                  name="account" 
                  size={32} 
                  color={COLORS.gray400} 
                />
              </View>
            )}
            {hasAllergies && (
              <View style={styles.allergyBadge}>
                <MaterialCommunityIcons 
                  name="alert-circle" 
                  size={12} 
                  color={COLORS.textInverse} 
                />
              </View>
            )}
          </View>
          
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{client.name}</Text>
            <View style={styles.clientDetails}>
              {client.email && (
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons 
                    name="email-outline" 
                    size={14} 
                    color={COLORS.textSecondary} 
                  />
                  <Text style={styles.detailText}>{client.email}</Text>
                </View>
              )}
              {client.phone && (
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons 
                    name="phone-outline" 
                    size={14} 
                    color={COLORS.textSecondary} 
                  />
                  <Text style={styles.detailText}>{client.phone}</Text>
                </View>
              )}
            </View>
          </View>

          <TouchableOpacity style={styles.moreButton}>
            <MaterialCommunityIcons 
              name="dots-vertical" 
              size={20} 
              color={COLORS.textSecondary} 
            />
          </TouchableOpacity>
        </View>

        <View style={styles.clientStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{totalVisits}</Text>
            <Text style={styles.statLabel}>Visitas</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{lastVisit}</Text>
            <Text style={styles.statLabel}>Última visita</Text>
          </View>
          {client.preferences.preferredStylist && (
            <>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <MaterialCommunityIcons 
                  name="account-heart" 
                  size={16} 
                  color={COLORS.primary} 
                />
                <Text style={styles.statLabel}>Estilista preferido</Text>
              </View>
            </>
          )}
        </View>

        {hasAllergies && (
          <View style={styles.allergyWarning}>
            <MaterialCommunityIcons 
              name="alert-circle-outline" 
              size={16} 
              color={COLORS.warning} 
            />
            <Text style={styles.allergyText}>
              {client.allergies.length} alergia{client.allergies.length > 1 ? 's' : ''} registrada{client.allergies.length > 1 ? 's' : ''}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const EmptyState: React.FC = () => (
    <View style={styles.emptyState}>
      <MaterialCommunityIcons 
        name="account-search" 
        size={64} 
        color={COLORS.gray300} 
      />
      <Text style={styles.emptyStateTitle}>
        {searchQuery ? 'No se encontraron clientes' : 'No hay clientes registrados'}
      </Text>
      <Text style={styles.emptyStateSubtitle}>
        {searchQuery 
          ? 'Intenta con otros términos de búsqueda'
          : 'Agrega tu primer cliente para comenzar'
        }
      </Text>
      {!searchQuery && (
        <TouchableOpacity style={styles.addFirstClientButton} onPress={handleAddClient}>
          <Text style={styles.addFirstClientText}>Agregar Cliente</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Barra de búsqueda */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MaterialCommunityIcons 
            name="magnify" 
            size={20} 
            color={COLORS.gray400}
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar clientes..."
            placeholderTextColor={COLORS.gray400}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity 
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <MaterialCommunityIcons 
                name="close-circle" 
                size={20} 
                color={COLORS.gray400}
              />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity style={styles.addButton} onPress={handleAddClient}>
          <MaterialCommunityIcons 
            name="plus" 
            size={24} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>

      {/* Lista de clientes */}
      <FlatList
        data={filteredClients}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <ClientCard client={item} />}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={EmptyState}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.background,
    gap: SPACING.sm,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: SPACING.lg,
    paddingTop: SPACING.md,
  },
  clientCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  clientImageContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  clientImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  clientImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  allergyBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.warning,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.background,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  clientDetails: {
    gap: 2,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  detailText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  moreButton: {
    padding: SPACING.xs,
  },
  clientStats: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray100,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  statLabel: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 20,
    backgroundColor: COLORS.gray200,
    marginHorizontal: SPACING.sm,
  },
  allergyWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.warning + '10',
    borderRadius: BORDER_RADIUS.sm,
    padding: SPACING.sm,
    marginTop: SPACING.sm,
    gap: SPACING.xs,
  },
  allergyText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.warning,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.xxl,
  },
  emptyStateTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyStateSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  addFirstClientButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    marginTop: SPACING.lg,
  },
  addFirstClientText: {
    color: COLORS.textInverse,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default ClientsScreen;
