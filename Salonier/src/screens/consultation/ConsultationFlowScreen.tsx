import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  BackHandler,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

import { useAuth } from '../../contexts/AuthContext';
import { ConsultationFlow, ConsultationStep, ConsultationFlowData } from '../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants';

// Importar pantallas del flujo
import ClientSelectionStep from './steps/ClientSelectionStep';
import SafetyProtocolStep from './steps/SafetyProtocolStep';
import ImageCaptureStep from './steps/ImageCaptureStep';
import AIAnalysisStep from './steps/AIAnalysisStep';
import ColorTargetStep from './steps/ColorTargetStep';

type ConsultationFlowScreenNavigationProp = StackNavigationProp<any, 'ConsultationFlow'>;
type ConsultationFlowScreenRouteProp = RouteProp<any, 'ConsultationFlow'>;

interface Props {
  navigation: ConsultationFlowScreenNavigationProp;
  route: ConsultationFlowScreenRouteProp;
}

const STEPS: { key: ConsultationStep; title: string; icon: string }[] = [
  { key: 'client-selection', title: 'Cliente', icon: 'account-search' },
  { key: 'safety-protocol', title: 'Seguridad', icon: 'shield-check' },
  { key: 'image-capture', title: 'Captura', icon: 'camera' },
  { key: 'ai-analysis', title: 'Análisis IA', icon: 'brain' },
  { key: 'color-target', title: 'Color Objetivo', icon: 'palette' },
  { key: 'formula-generation', title: 'Fórmula', icon: 'flask' },
  { key: 'documentation', title: 'Documentar', icon: 'file-document' },
];

const ConsultationFlowScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuth();
  const [consultationFlow, setConsultationFlow] = useState<ConsultationFlow>({
    id: `consultation-${Date.now()}`,
    clientId: '',
    stylistId: user?.id || '',
    currentStep: 'client-selection',
    startedAt: new Date().toISOString(),
    data: {},
  });

  const [isLoading, setIsLoading] = useState(false);

  // Manejar botón atrás del dispositivo
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [consultationFlow.currentStep]);

  const handleBackPress = (): boolean => {
    if (consultationFlow.currentStep === 'client-selection') {
      handleExitConsultation();
      return true;
    } else {
      goToPreviousStep();
      return true;
    }
  };

  const handleExitConsultation = () => {
    Alert.alert(
      'Salir de la consulta',
      '¿Estás seguro de que quieres salir? Se perderá el progreso actual.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Salir', 
          style: 'destructive',
          onPress: () => navigation.goBack()
        },
      ]
    );
  };

  const getCurrentStepIndex = (): number => {
    return STEPS.findIndex(step => step.key === consultationFlow.currentStep);
  };

  const goToNextStep = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex < STEPS.length - 1) {
      const nextStep = STEPS[currentIndex + 1];
      setConsultationFlow(prev => ({
        ...prev,
        currentStep: nextStep.key,
      }));
    } else {
      // Completar consulta
      completeConsultation();
    }
  };

  const goToPreviousStep = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      const previousStep = STEPS[currentIndex - 1];
      setConsultationFlow(prev => ({
        ...prev,
        currentStep: previousStep.key,
      }));
    }
  };

  const updateFlowData = (stepData: Partial<ConsultationFlowData>) => {
    setConsultationFlow(prev => ({
      ...prev,
      data: {
        ...prev.data,
        ...stepData,
      },
    }));
  };

  const completeConsultation = () => {
    setConsultationFlow(prev => ({
      ...prev,
      currentStep: 'completed',
      completedAt: new Date().toISOString(),
    }));

    Alert.alert(
      'Consulta completada',
      'La consulta se ha guardado exitosamente.',
      [
        { 
          text: 'Ver resumen', 
          onPress: () => {
            // Navegar a resumen de consulta
            console.log('Ver resumen de consulta');
          }
        },
        { 
          text: 'Nueva consulta', 
          onPress: () => {
            // Reiniciar flujo
            navigation.replace('ConsultationFlow');
          }
        },
        { 
          text: 'Volver al inicio', 
          onPress: () => navigation.navigate('Dashboard')
        },
      ]
    );
  };

  const renderStepIndicator = () => {
    const currentIndex = getCurrentStepIndex();
    
    return (
      <View style={styles.stepIndicator}>
        {STEPS.map((step, index) => (
          <View key={step.key} style={styles.stepItem}>
            <View style={[
              styles.stepCircle,
              index <= currentIndex && styles.stepCircleActive,
              index === currentIndex && styles.stepCircleCurrent,
            ]}>
              <MaterialCommunityIcons 
                name={step.icon as any} 
                size={16} 
                color={index <= currentIndex ? COLORS.textInverse : COLORS.gray400}
              />
            </View>
            <Text style={[
              styles.stepLabel,
              index <= currentIndex && styles.stepLabelActive,
            ]}>
              {step.title}
            </Text>
            {index < STEPS.length - 1 && (
              <View style={[
                styles.stepConnector,
                index < currentIndex && styles.stepConnectorActive,
              ]} />
            )}
          </View>
        ))}
      </View>
    );
  };

  const renderCurrentStep = () => {
    const stepProps = {
      consultationFlow,
      updateFlowData,
      goToNextStep,
      goToPreviousStep,
      isLoading,
      setIsLoading,
    };

    switch (consultationFlow.currentStep) {
      case 'client-selection':
        return <ClientSelectionStep {...stepProps} />;
      case 'safety-protocol':
        return <SafetyProtocolStep {...stepProps} />;
      case 'image-capture':
        return <ImageCaptureStep {...stepProps} />;
      case 'ai-analysis':
        return <AIAnalysisStep {...stepProps} />;
      case 'color-target':
        return <ColorTargetStep {...stepProps} />;
      default:
        return (
          <View style={styles.placeholderStep}>
            <MaterialCommunityIcons
              name="wrench"
              size={48}
              color={COLORS.gray400}
            />
            <Text style={styles.placeholderTitle}>
              Paso en desarrollo
            </Text>
            <Text style={styles.placeholderText}>
              Esta funcionalidad estará disponible próximamente
            </Text>
            <TouchableOpacity
              style={styles.continueButton}
              onPress={goToNextStep}
            >
              <Text style={styles.continueButtonText}>Continuar</Text>
            </TouchableOpacity>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleBackPress}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={24} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Nueva Consulta</Text>
          <Text style={styles.headerSubtitle}>
            Paso {getCurrentStepIndex() + 1} de {STEPS.length}
          </Text>
        </View>

        <TouchableOpacity 
          style={styles.exitButton}
          onPress={handleExitConsultation}
        >
          <MaterialCommunityIcons 
            name="close" 
            size={24} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>

      {/* Indicador de pasos */}
      {renderStepIndicator()}

      {/* Contenido del paso actual */}
      <View style={styles.stepContent}>
        {renderCurrentStep()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingTop: 50, // Para status bar
    paddingBottom: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  backButton: {
    padding: SPACING.sm,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textInverse,
  },
  headerSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textInverse,
    opacity: 0.8,
  },
  exitButton: {
    padding: SPACING.sm,
  },
  stepIndicator: {
    flexDirection: 'row',
    backgroundColor: COLORS.background,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepItem: {
    alignItems: 'center',
    flex: 1,
    position: 'relative',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.gray200,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  stepCircleActive: {
    backgroundColor: COLORS.primary,
  },
  stepCircleCurrent: {
    backgroundColor: COLORS.secondary,
  },
  stepLabel: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textTertiary,
    textAlign: 'center',
  },
  stepLabelActive: {
    color: COLORS.textPrimary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  stepConnector: {
    position: 'absolute',
    top: 16,
    left: '60%',
    right: '-40%',
    height: 2,
    backgroundColor: COLORS.gray200,
  },
  stepConnectorActive: {
    backgroundColor: COLORS.primary,
  },
  stepContent: {
    flex: 1,
  },
  placeholderStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  placeholderTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  placeholderText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  continueButtonText: {
    color: COLORS.textInverse,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default ConsultationFlowScreen;
