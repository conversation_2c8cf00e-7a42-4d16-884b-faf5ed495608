import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

import { ConsultationFlow, ConsultationFlowData, ColorTechnique } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

const { width } = Dimensions.get('window');

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

interface ReferenceImage {
  id: string;
  uri: string;
  timestamp: string;
  analyzedColors?: {
    dominant: string;
    secondary: string[];
    level: number;
    tone: string;
  };
}

const COLOR_TECHNIQUES: { key: ColorTechnique; name: string; description: string; icon: string }[] = [
  {
    key: 'full-color',
    name: 'Color Completo',
    description: 'Cambio total de color en todo el cabello',
    icon: 'palette',
  },
  {
    key: 'highlights',
    name: 'Mechas',
    description: 'Aclarado de mechones selectos',
    icon: 'brush',
  },
  {
    key: 'balayage',
    name: 'Balayage',
    description: 'Técnica de pintura a mano alzada',
    icon: 'brush-variant',
  },
  {
    key: 'ombre',
    name: 'Ombré',
    description: 'Degradado de oscuro a claro',
    icon: 'gradient-vertical',
  },
  {
    key: 'babylights',
    name: 'Babylights',
    description: 'Mechas muy finas y naturales',
    icon: 'lightbulb-outline',
  },
  {
    key: 'color-correction',
    name: 'Corrección',
    description: 'Corrección de color previo',
    icon: 'auto-fix',
  },
];

const ColorTargetStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [referenceImages, setReferenceImages] = useState<ReferenceImage[]>([]);
  const [targetDescription, setTargetDescription] = useState('');
  const [selectedTechnique, setSelectedTechnique] = useState<ColorTechnique>('balayage');
  const [clientExpectations, setClientExpectations] = useState('');
  const [timelineExpected, setTimelineExpected] = useState('');
  const [budgetRange, setBudgetRange] = useState({ min: 0, max: 0 });
  const [showViabilityAnalysis, setShowViabilityAnalysis] = useState(false);

  // Obtener datos del análisis previo
  const currentHairAnalysis = consultationFlow.data.aiAnalysis?.results;

  const addReferenceImage = async (source: 'camera' | 'gallery') => {
    if (referenceImages.length >= 3) {
      Alert.alert('Límite alcanzado', 'Puedes subir máximo 3 imágenes de referencia');
      return;
    }

    try {
      let result;
      
      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permisos necesarios', 'Necesitamos acceso a la cámara');
          return;
        }
        
        result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permisos necesarios', 'Necesitamos acceso a la galería');
          return;
        }
        
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        const newImage: ReferenceImage = {
          id: `ref-${Date.now()}`,
          uri: result.assets[0].uri,
          timestamp: new Date().toISOString(),
        };

        // Simular análisis de color
        setTimeout(() => {
          analyzeReferenceImage(newImage.id);
        }, 1000);

        setReferenceImages(prev => [...prev, newImage]);
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo agregar la imagen');
    }
  };

  const analyzeReferenceImage = (imageId: string) => {
    // Mock de análisis de color
    const mockColors = {
      dominant: ['#8B4513', '#D2691E', '#F4A460', '#DEB887', '#F5DEB3'][Math.floor(Math.random() * 5)],
      secondary: ['#CD853F', '#DAA520', '#B8860B'],
      level: Math.floor(Math.random() * 4) + 5, // Niveles 5-8 más comunes en referencias
      tone: ['Dorado', 'Cenizo', 'Cobrizo', 'Natural'][Math.floor(Math.random() * 4)],
    };

    setReferenceImages(prev => prev.map(img => 
      img.id === imageId 
        ? { ...img, analyzedColors: mockColors }
        : img
    ));
  };

  const removeReferenceImage = (imageId: string) => {
    setReferenceImages(prev => prev.filter(img => img.id !== imageId));
  };

  const analyzeViability = () => {
    if (!currentHairAnalysis || referenceImages.length === 0) {
      Alert.alert('Información insuficiente', 'Necesitas al menos una imagen de referencia');
      return;
    }

    setShowViabilityAnalysis(true);
  };

  const getViabilityAnalysis = () => {
    if (!currentHairAnalysis || referenceImages.length === 0) return null;

    const currentLevel = currentHairAnalysis.naturalLevel.value;
    const targetLevel = referenceImages[0]?.analyzedColors?.level || currentLevel;
    const levelDifference = targetLevel - currentLevel;
    
    const isLightening = levelDifference > 0;
    const isDarkening = levelDifference < 0;
    const isMinorChange = Math.abs(levelDifference) <= 1;
    const isMajorChange = Math.abs(levelDifference) >= 3;

    let viability = 'high';
    let sessionsNeeded = 1;
    let warnings: string[] = [];
    let recommendations: string[] = [];

    if (isLightening && isMajorChange) {
      viability = 'medium';
      sessionsNeeded = Math.ceil(Math.abs(levelDifference) / 2);
      warnings.push('Requiere decoloración progresiva');
      warnings.push('Riesgo de daño capilar');
      recommendations.push('Tratamientos reconstructores entre sesiones');
      recommendations.push('Considerar técnica gradual (balayage/mechas)');
    }

    if (currentHairAnalysis.condition.damage !== 'none') {
      viability = viability === 'high' ? 'medium' : 'low';
      warnings.push('Cabello previamente dañado');
      recommendations.push('Tratamiento previo obligatorio');
    }

    if (currentHairAnalysis.resistance.value === 'high') {
      sessionsNeeded += 1;
      recommendations.push('Tiempo de procesamiento extendido');
    }

    return {
      viability: viability as 'high' | 'medium' | 'low',
      sessionsNeeded,
      estimatedTime: sessionsNeeded * 3, // 3 horas por sesión
      estimatedCost: sessionsNeeded * 150, // €150 por sesión
      warnings,
      recommendations,
      levelDifference,
      isLightening,
      isDarkening,
    };
  };

  const handleContinue = () => {
    if (referenceImages.length === 0 && !targetDescription.trim()) {
      Alert.alert(
        'Información requerida',
        'Por favor agrega al menos una imagen de referencia o describe el color deseado'
      );
      return;
    }

    if (!selectedTechnique) {
      Alert.alert('Técnica requerida', 'Por favor selecciona una técnica de coloración');
      return;
    }

    // Guardar datos del color objetivo
    updateFlowData({
      colorTarget: {
        targetImages: referenceImages.map(img => img.uri),
        targetDescription,
        targetLevel: referenceImages[0]?.analyzedColors?.level,
        targetTone: referenceImages[0]?.analyzedColors?.tone,
        technique: selectedTechnique,
        inspiration: referenceImages.map(img => img.analyzedColors?.dominant || ''),
        clientExpectations,
        timelineExpected,
        budgetRange: budgetRange.max > 0 ? budgetRange : undefined,
      },
    });

    goToNextStep();
  };

  const viabilityAnalysis = getViabilityAnalysis();

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Define tu Color Ideal</Text>
          <Text style={styles.subtitle}>
            Muéstranos qué resultado quieres lograr con imágenes de referencia
          </Text>
        </View>

        {/* Imágenes de referencia */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Imágenes de Referencia</Text>
          <Text style={styles.sectionSubtitle}>
            Sube hasta 3 imágenes del color que te gusta (máximo 3)
          </Text>
          
          <View style={styles.imagesContainer}>
            {referenceImages.map((image) => (
              <View key={image.id} style={styles.referenceImageCard}>
                <Image source={{ uri: image.uri }} style={styles.referenceImage} />
                <TouchableOpacity 
                  style={styles.removeImageButton}
                  onPress={() => removeReferenceImage(image.id)}
                >
                  <MaterialCommunityIcons name="close" size={16} color={COLORS.textInverse} />
                </TouchableOpacity>
                
                {image.analyzedColors && (
                  <View style={styles.colorAnalysis}>
                    <View 
                      style={[
                        styles.colorSwatch, 
                        { backgroundColor: image.analyzedColors.dominant }
                      ]} 
                    />
                    <Text style={styles.colorInfo}>
                      Nivel {image.analyzedColors.level} - {image.analyzedColors.tone}
                    </Text>
                  </View>
                )}
              </View>
            ))}
            
            {referenceImages.length < 3 && (
              <View style={styles.addImageContainer}>
                <TouchableOpacity 
                  style={styles.addImageButton}
                  onPress={() => addReferenceImage('camera')}
                >
                  <MaterialCommunityIcons name="camera" size={24} color={COLORS.primary} />
                  <Text style={styles.addImageText}>Cámara</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.addImageButton}
                  onPress={() => addReferenceImage('gallery')}
                >
                  <MaterialCommunityIcons name="image" size={24} color={COLORS.primary} />
                  <Text style={styles.addImageText}>Galería</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>

        {/* Descripción del objetivo */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Describe tu Objetivo</Text>
          <TextInput
            style={styles.descriptionInput}
            placeholder="Describe el color que quieres lograr..."
            placeholderTextColor={COLORS.gray400}
            value={targetDescription}
            onChangeText={setTargetDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Técnica preferida */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Técnica de Coloración</Text>
          <View style={styles.techniquesContainer}>
            {COLOR_TECHNIQUES.map((technique) => (
              <TouchableOpacity
                key={technique.key}
                style={[
                  styles.techniqueCard,
                  selectedTechnique === technique.key && styles.techniqueCardSelected
                ]}
                onPress={() => setSelectedTechnique(technique.key)}
              >
                <MaterialCommunityIcons 
                  name={technique.icon as any} 
                  size={24} 
                  color={selectedTechnique === technique.key ? COLORS.textInverse : COLORS.primary}
                />
                <Text style={[
                  styles.techniqueName,
                  selectedTechnique === technique.key && styles.techniqueNameSelected
                ]}>
                  {technique.name}
                </Text>
                <Text style={[
                  styles.techniqueDescription,
                  selectedTechnique === technique.key && styles.techniqueDescriptionSelected
                ]}>
                  {technique.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Análisis de viabilidad */}
        {referenceImages.length > 0 && currentHairAnalysis && (
          <View style={styles.section}>
            <View style={styles.viabilityHeader}>
              <Text style={styles.sectionTitle}>Análisis de Viabilidad</Text>
              <TouchableOpacity 
                style={styles.analyzeButton}
                onPress={analyzeViability}
              >
                <MaterialCommunityIcons name="brain" size={16} color={COLORS.textInverse} />
                <Text style={styles.analyzeButtonText}>Analizar</Text>
              </TouchableOpacity>
            </View>

            {showViabilityAnalysis && viabilityAnalysis && (
              <View style={styles.viabilityResults}>
                <View style={[
                  styles.viabilityBadge,
                  { backgroundColor: 
                    viabilityAnalysis.viability === 'high' ? COLORS.success + '20' :
                    viabilityAnalysis.viability === 'medium' ? COLORS.warning + '20' :
                    COLORS.error + '20'
                  }
                ]}>
                  <Text style={[
                    styles.viabilityText,
                    { color:
                      viabilityAnalysis.viability === 'high' ? COLORS.success :
                      viabilityAnalysis.viability === 'medium' ? COLORS.warning :
                      COLORS.error
                    }
                  ]}>
                    Viabilidad: {
                      viabilityAnalysis.viability === 'high' ? 'Alta ✓' :
                      viabilityAnalysis.viability === 'medium' ? 'Media ⚠️' :
                      'Baja ⚠️'
                    }
                  </Text>
                </View>

                <View style={styles.viabilityDetails}>
                  <View style={styles.viabilityItem}>
                    <MaterialCommunityIcons name="clock-outline" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.viabilityItemText}>
                      {viabilityAnalysis.sessionsNeeded} sesión{viabilityAnalysis.sessionsNeeded > 1 ? 'es' : ''} 
                      ({viabilityAnalysis.estimatedTime}h total)
                    </Text>
                  </View>
                  
                  <View style={styles.viabilityItem}>
                    <MaterialCommunityIcons name="currency-eur" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.viabilityItemText}>
                      €{viabilityAnalysis.estimatedCost} estimado
                    </Text>
                  </View>

                  <View style={styles.viabilityItem}>
                    <MaterialCommunityIcons name="trending-up" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.viabilityItemText}>
                      {viabilityAnalysis.isLightening ? 'Aclarado' : viabilityAnalysis.isDarkening ? 'Oscurecimiento' : 'Cambio de tono'} 
                      ({Math.abs(viabilityAnalysis.levelDifference)} nivel{Math.abs(viabilityAnalysis.levelDifference) > 1 ? 'es' : ''})
                    </Text>
                  </View>
                </View>

                {viabilityAnalysis.warnings.length > 0 && (
                  <View style={styles.warningsContainer}>
                    <Text style={styles.warningsTitle}>⚠️ Consideraciones importantes:</Text>
                    {viabilityAnalysis.warnings.map((warning, index) => (
                      <Text key={index} style={styles.warningText}>• {warning}</Text>
                    ))}
                  </View>
                )}

                {viabilityAnalysis.recommendations.length > 0 && (
                  <View style={styles.recommendationsContainer}>
                    <Text style={styles.recommendationsTitle}>💡 Recomendaciones:</Text>
                    {viabilityAnalysis.recommendations.map((rec, index) => (
                      <Text key={index} style={styles.recommendationText}>• {rec}</Text>
                    ))}
                  </View>
                )}
              </View>
            )}
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={20} 
            color={COLORS.textSecondary} 
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[
            styles.continueButton,
            (referenceImages.length === 0 && !targetDescription.trim()) && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={referenceImages.length === 0 && !targetDescription.trim()}
        >
          <Text style={styles.continueButtonText}>Generar Fórmula</Text>
          <MaterialCommunityIcons 
            name="flask" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    paddingVertical: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  sectionSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  referenceImageCard: {
    position: 'relative',
    width: (width - SPACING.lg * 2 - SPACING.md * 2) / 3,
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.sm,
  },
  referenceImage: {
    width: '100%',
    height: 120,
    backgroundColor: COLORS.gray100,
  },
  removeImageButton: {
    position: 'absolute',
    top: SPACING.xs,
    right: SPACING.xs,
    backgroundColor: COLORS.error + 'CC',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorAnalysis: {
    padding: SPACING.sm,
    alignItems: 'center',
  },
  colorSwatch: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginBottom: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  colorInfo: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  addImageContainer: {
    width: (width - SPACING.lg * 2 - SPACING.md * 2) / 3,
    height: 120,
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 2,
    borderColor: COLORS.gray200,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  addImageButton: {
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  addImageText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  descriptionInput: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    minHeight: 100,
    ...SHADOWS.sm,
  },
  techniquesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  techniqueCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    width: (width - SPACING.lg * 2 - SPACING.sm) / 2,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    ...SHADOWS.sm,
  },
  techniqueCardSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  techniqueName: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xs,
    textAlign: 'center',
  },
  techniqueNameSelected: {
    color: COLORS.textInverse,
  },
  techniqueDescription: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  techniqueDescriptionSelected: {
    color: COLORS.textInverse,
    opacity: 0.9,
  },
  viabilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    gap: SPACING.xs,
  },
  analyzeButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  viabilityResults: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    ...SHADOWS.sm,
  },
  viabilityBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  viabilityText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  viabilityDetails: {
    marginBottom: SPACING.md,
  },
  viabilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  viabilityItemText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
  },
  warningsContainer: {
    backgroundColor: COLORS.warning + '10',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  warningsTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.warning,
    marginBottom: SPACING.sm,
  },
  warningText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.warning,
    marginBottom: 2,
  },
  recommendationsContainer: {
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
  },
  recommendationsTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.info,
    marginBottom: SPACING.sm,
  },
  recommendationText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.info,
    marginBottom: 2,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonDisabled: {
    backgroundColor: COLORS.gray300,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default ColorTargetStep;
