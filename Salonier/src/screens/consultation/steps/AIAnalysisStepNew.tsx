import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { ConsultationFlow, ConsultationFlowData, AIAnalysisResults, AIRecommendation, HairZoneAnalysis } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AIAnalysisStepNew: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [analysisPhase, setAnalysisPhase] = useState<'analyzing' | 'completed'>('analyzing');
  const [progressAnimation] = useState(new Animated.Value(0));
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResults | null>(null);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);

  useEffect(() => {
    startAnalysis();
  }, []);

  const startAnalysis = () => {
    setIsLoading(true);
    setAnalysisPhase('analyzing');

    // Animación de progreso
    Animated.timing(progressAnimation, {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();

    // Simular análisis IA
    setTimeout(() => {
      const mockResults = generateMockAnalysis();
      const mockRecommendations = generateMockRecommendations(mockResults);
      
      setAnalysisResults(mockResults);
      setRecommendations(mockRecommendations);
      setAnalysisPhase('completed');
      setIsLoading(false);
    }, 3500);
  };

  const generateMockAnalysis = (): AIAnalysisResults => {
    const capturedImages = consultationFlow.data.imageCapture?.capturedImages || [];
    const hasMultipleAngles = capturedImages.length >= 3;
    const confidence = hasMultipleAngles ? 0.85 + Math.random() * 0.1 : 0.7 + Math.random() * 0.15;

    // Generar análisis por zonas
    const generateZoneAnalysis = (zone: 'roots' | 'mids' | 'ends'): HairZoneAnalysis => {
      const baseLevel = Math.floor(Math.random() * 4) + 3; // Niveles 3-6
      const hasArtificialColor = Math.random() > 0.4;
      
      return {
        zone,
        naturalLevel: {
          value: baseLevel,
          confidence: confidence,
          description: `Nivel natural ${baseLevel} detectado`,
        },
        artificialColor: {
          detected: hasArtificialColor && zone !== 'roots',
          level: hasArtificialColor ? baseLevel + 2 : undefined,
          tone: hasArtificialColor ? 'Dorado' : undefined,
        },
        undertone: {
          value: Math.random() > 0.6 ? 'warm' : Math.random() > 0.5 ? 'cool' : 'neutral',
          confidence: confidence - 0.1,
          description: 'Subtono detectado',
        },
        porosity: {
          value: zone === 'ends' ? 'high' : Math.random() > 0.5 ? 'medium' : 'low',
          confidence: confidence,
          description: 'Capacidad de absorción',
          uniformity: 'even',
        },
        elasticity: {
          value: zone === 'ends' ? 'poor' : 'normal',
          confidence: confidence - 0.1,
          stretchTest: false,
        },
        condition: {
          damage: zone === 'ends' ? 'moderate' : 'minimal',
          dryness: zone === 'ends' ? 'moderate' : 'mild',
          breakage: 'minimal',
          chemicalResidues: hasArtificialColor,
          metalDeposits: false,
        },
        ...(zone === 'roots' && {
          rootsSpecific: {
            growthLength: Math.floor(Math.random() * 20) + 10,
            grayPercentage: Math.floor(Math.random() * 40),
            grayDistribution: 'scattered' as const,
            naturalTexture: 'wavy' as const,
          },
        }),
      };
    };

    const zoneAnalysis = [
      generateZoneAnalysis('roots'),
      generateZoneAnalysis('mids'),
      generateZoneAnalysis('ends'),
    ];

    return {
      overallAssessment: {
        hairType: 'color-treated',
        complexity: 'moderate',
        recommendedApproach: 'Considerar historial de color',
      },
      zoneAnalysis,
      diameter: {
        value: 'medium',
        confidence: confidence - 0.15,
        description: 'Grosor medio',
      },
      density: {
        value: 'medium',
        confidence: confidence - 0.1,
        description: 'Densidad media',
      },
      uniformityAnalysis: {
        levelVariation: 1,
        toneVariation: false,
        porosityVariation: true,
        recommendations: ['Usar relleno de porosidad en puntas'],
      },
      chemicalHistory: {
        previousColor: true,
        bleachHistory: false,
        permanentWave: false,
        relaxer: false,
        estimatedLastService: '4-6 semanas',
        compatibilityWarnings: [],
      },
    };
  };

  const generateMockRecommendations = (results: AIAnalysisResults): AIRecommendation[] => {
    return [
      {
        id: 'rec-1',
        type: 'technique',
        priority: 'high',
        title: 'Enfoque Recomendado',
        description: results.overallAssessment.recommendedApproach,
        reasoning: `Tipo de cabello: ${results.overallAssessment.hairType}`,
        actionRequired: true,
      },
      {
        id: 'rec-2',
        type: 'product',
        priority: 'medium',
        title: 'Tratamiento de Porosidad',
        description: 'Aplicar relleno de porosidad en puntas antes de colorar',
        reasoning: 'Porosidad variable detectada entre zonas',
        actionRequired: true,
      },
    ];
  };

  const getAverageConfidence = (): number => {
    if (!analysisResults?.zoneAnalysis || analysisResults.zoneAnalysis.length === 0) return 0;
    return analysisResults.zoneAnalysis.reduce((sum, zone) => 
      sum + zone.naturalLevel.confidence, 0) / analysisResults.zoneAnalysis.length;
  };

  const handleContinue = () => {
    if (!analysisResults) return;

    const averageConfidence = getAverageConfidence();

    updateFlowData({
      aiAnalysis: {
        analysisId: `analysis-${Date.now()}`,
        processedAt: new Date().toISOString(),
        processingTime: 3500,
        confidence: averageConfidence,
        results: analysisResults,
        recommendations,
      },
    });

    goToNextStep();
  };

  if (analysisPhase === 'analyzing') {
    return (
      <View style={styles.analyzingContainer}>
        <View style={styles.analyzingContent}>
          <View style={styles.brainIcon}>
            <MaterialCommunityIcons
              name="brain"
              size={64}
              color={COLORS.primary}
            />
          </View>

          <Text style={styles.analyzingTitle}>Analizando con IA</Text>
          <Text style={styles.analyzingSubtitle}>
            Procesando {consultationFlow.data.imageCapture?.capturedImages.length || 0} imágenes...
          </Text>

          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>

          <Text style={styles.progressText}>
            Analizando raíces, medios y puntas...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.successIcon}>
            <MaterialCommunityIcons name="check-circle" size={48} color={COLORS.success} />
          </View>
          <Text style={styles.title}>Análisis Completado</Text>
          <Text style={styles.subtitle}>
            IA ha analizado {consultationFlow.data.imageCapture?.capturedImages.length || 0} imágenes
          </Text>
        </View>

        {/* Resumen */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Resumen del Análisis</Text>
          
          <View style={styles.summaryCard}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Tipo de Cabello</Text>
              <Text style={styles.summaryValue}>
                {analysisResults?.overallAssessment.hairType === 'virgin' ? 'Virgen' :
                 analysisResults?.overallAssessment.hairType === 'color-treated' ? 'Con color' :
                 analysisResults?.overallAssessment.hairType === 'chemically-processed' ? 'Procesado' : 'Dañado'}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Complejidad</Text>
              <Text style={styles.summaryValue}>
                {analysisResults?.overallAssessment.complexity === 'simple' ? 'Simple' :
                 analysisResults?.overallAssessment.complexity === 'moderate' ? 'Moderada' :
                 analysisResults?.overallAssessment.complexity === 'complex' ? 'Compleja' : 'Alto riesgo'}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Zonas Analizadas</Text>
              <Text style={styles.summaryValue}>
                {analysisResults?.zoneAnalysis?.length || 0}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Confianza Promedio</Text>
              <Text style={styles.summaryValue}>
                {Math.round(getAverageConfidence() * 100)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Recomendaciones */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recomendaciones</Text>
          {recommendations.map((rec) => (
            <View key={rec.id} style={styles.recommendationCard}>
              <View style={styles.recommendationHeader}>
                <MaterialCommunityIcons 
                  name="lightbulb" 
                  size={20} 
                  color={rec.priority === 'high' ? COLORS.warning : COLORS.info}
                />
                <Text style={styles.recommendationTitle}>{rec.title}</Text>
              </View>
              <Text style={styles.recommendationDescription}>
                {rec.description}
              </Text>
              <Text style={styles.recommendationReasoning}>
                {rec.reasoning}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={20} 
            color={COLORS.textSecondary} 
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.continueButton}
          onPress={handleContinue}
        >
          <Text style={styles.continueButtonText}>Validar Análisis</Text>
          <MaterialCommunityIcons 
            name="account-check" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  analyzingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundSecondary,
    paddingHorizontal: SPACING.xl,
  },
  analyzingContent: {
    alignItems: 'center',
    width: '100%',
  },
  brainIcon: {
    marginBottom: SPACING.xl,
  },
  analyzingTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  analyzingSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  progressContainer: {
    width: '100%',
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.lg,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textTertiary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.lg,
  },
  successIcon: {
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  section: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  summaryCard: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  summaryLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  summaryValue: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  recommendationCard: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  recommendationTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  recommendationDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  recommendationReasoning: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textTertiary,
    fontStyle: 'italic',
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default AIAnalysisStepNew;
