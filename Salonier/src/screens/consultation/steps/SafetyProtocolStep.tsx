import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { mockClients, getClientById } from '../../../data/mock/clients';
import { ConsultationFlow, ConsultationFlowData, SafetyChecklistItem } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const SAFETY_CHECKLIST: SafetyChecklistItem[] = [
  {
    id: 'consent-obtained',
    description: 'Consentimiento informado obtenido y firmado',
    completed: false,
    required: true,
  },
  {
    id: 'allergies-reviewed',
    description: 'Alergias conocidas revisadas y documentadas',
    completed: false,
    required: true,
  },
  {
    id: 'patch-test-discussed',
    description: 'Test de parche discutido con el cliente',
    completed: false,
    required: true,
  },
  {
    id: 'hair-condition-assessed',
    description: 'Condición del cabello evaluada visualmente',
    completed: false,
    required: true,
  },
  {
    id: 'previous-treatments-reviewed',
    description: 'Tratamientos químicos previos revisados',
    completed: false,
    required: true,
  },
  {
    id: 'client-expectations-discussed',
    description: 'Expectativas del cliente discutidas y alineadas',
    completed: false,
    required: false,
  },
  {
    id: 'aftercare-explained',
    description: 'Cuidados posteriores explicados al cliente',
    completed: false,
    required: false,
  },
];

const SafetyProtocolStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [checklist, setChecklist] = useState<SafetyChecklistItem[]>(SAFETY_CHECKLIST);
  const [consentObtained, setConsentObtained] = useState(false);
  const [patchTestRequired, setPatchTestRequired] = useState(false);
  const [patchTestCompleted, setPatchTestCompleted] = useState(false);
  const [patchTestResult, setPatchTestResult] = useState<'positive' | 'negative' | 'pending'>('pending');

  // Obtener información del cliente
  const clientData = consultationFlow.data.clientSelection?.isGuestClient 
    ? consultationFlow.data.clientSelection.guestClientInfo
    : getClientById(consultationFlow.data.clientSelection?.selectedClientId || '');

  const clientAllergies = consultationFlow.data.clientSelection?.isGuestClient 
    ? []
    : (clientData as any)?.allergies || [];

  useEffect(() => {
    // Auto-completar algunos checks si hay información disponible
    if (clientAllergies.length > 0) {
      updateChecklistItem('allergies-reviewed', true);
    }
  }, [clientAllergies]);

  const updateChecklistItem = (id: string, completed: boolean) => {
    setChecklist(prev => prev.map(item => 
      item.id === id ? { ...item, completed } : item
    ));
  };

  const getCompletionStats = () => {
    const requiredItems = checklist.filter(item => item.required);
    const completedRequired = requiredItems.filter(item => item.completed);
    const optionalItems = checklist.filter(item => !item.required);
    const completedOptional = optionalItems.filter(item => item.completed);

    return {
      requiredTotal: requiredItems.length,
      requiredCompleted: completedRequired.length,
      optionalTotal: optionalItems.length,
      optionalCompleted: completedOptional.length,
      allRequiredCompleted: completedRequired.length === requiredItems.length,
    };
  };

  const handlePatchTestToggle = (required: boolean) => {
    setPatchTestRequired(required);
    if (!required) {
      setPatchTestCompleted(false);
      setPatchTestResult('pending');
    }
  };

  const handleContinue = () => {
    const stats = getCompletionStats();
    
    if (!stats.allRequiredCompleted) {
      Alert.alert(
        'Protocolo incompleto',
        'Por favor completa todos los elementos obligatorios del protocolo de seguridad antes de continuar.'
      );
      return;
    }

    if (!consentObtained) {
      Alert.alert(
        'Consentimiento requerido',
        'El consentimiento informado es obligatorio para continuar con la consulta.'
      );
      return;
    }

    if (patchTestRequired && !patchTestCompleted) {
      Alert.alert(
        'Test de parche pendiente',
        'Has indicado que se requiere test de parche pero no está completado. ¿Deseas continuar de todos modos?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Continuar', onPress: proceedToNext },
        ]
      );
      return;
    }

    if (patchTestCompleted && patchTestResult === 'positive') {
      Alert.alert(
        'Test de parche positivo',
        'El test de parche ha dado positivo. No se recomienda continuar con el servicio de coloración.',
        [
          { text: 'Entendido', style: 'cancel' },
          { 
            text: 'Continuar bajo responsabilidad', 
            style: 'destructive',
            onPress: proceedToNext 
          },
        ]
      );
      return;
    }

    proceedToNext();
  };

  const proceedToNext = () => {
    // Guardar datos del protocolo de seguridad
    updateFlowData({
      safetyProtocol: {
        consentObtained,
        consentDate: new Date().toISOString(),
        patchTestRequired,
        patchTestCompleted,
        patchTestResult: patchTestCompleted ? patchTestResult : undefined,
        patchTestDate: patchTestCompleted ? new Date().toISOString() : undefined,
        allergiesChecked: true,
        knownAllergies: clientAllergies.map(allergy => allergy.substance),
        safetyChecklist: checklist,
      },
    });

    goToNextStep();
  };

  const ChecklistItem: React.FC<{ item: SafetyChecklistItem }> = ({ item }) => (
    <TouchableOpacity 
      style={styles.checklistItem}
      onPress={() => updateChecklistItem(item.id, !item.completed)}
    >
      <View style={[
        styles.checkbox,
        item.completed && styles.checkboxCompleted,
        item.required && !item.completed && styles.checkboxRequired,
      ]}>
        {item.completed && (
          <MaterialCommunityIcons 
            name="check" 
            size={16} 
            color={COLORS.textInverse} 
          />
        )}
      </View>
      
      <View style={styles.checklistContent}>
        <Text style={[
          styles.checklistText,
          item.completed && styles.checklistTextCompleted,
        ]}>
          {item.description}
        </Text>
        {item.required && (
          <Text style={styles.requiredLabel}>Obligatorio</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const stats = getCompletionStats();

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Protocolo de Seguridad</Text>
          <Text style={styles.subtitle}>
            Verificación obligatoria antes de iniciar cualquier servicio de coloración
          </Text>
        </View>

        {/* Información del cliente */}
        <View style={styles.clientInfo}>
          <View style={styles.clientHeader}>
            <MaterialCommunityIcons 
              name="account-circle" 
              size={24} 
              color={COLORS.primary} 
            />
            <Text style={styles.clientName}>
              {consultationFlow.data.clientSelection?.isGuestClient 
                ? consultationFlow.data.clientSelection.guestClientInfo?.name
                : (clientData as any)?.name
              }
            </Text>
          </View>
          
          {clientAllergies.length > 0 && (
            <View style={styles.allergiesAlert}>
              <MaterialCommunityIcons 
                name="alert-circle" 
                size={20} 
                color={COLORS.warning} 
              />
              <View style={styles.allergiesContent}>
                <Text style={styles.allergiesTitle}>
                  ⚠️ Alergias conocidas ({clientAllergies.length})
                </Text>
                {clientAllergies.map((allergy, index) => (
                  <Text key={index} style={styles.allergyItem}>
                    • {allergy.substance} ({allergy.severity})
                  </Text>
                ))}
              </View>
            </View>
          )}
        </View>

        {/* Progreso */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Progreso del protocolo</Text>
          <View style={styles.progressStats}>
            <View style={styles.progressItem}>
              <Text style={styles.progressNumber}>
                {stats.requiredCompleted}/{stats.requiredTotal}
              </Text>
              <Text style={styles.progressLabel}>Obligatorios</Text>
            </View>
            <View style={styles.progressItem}>
              <Text style={styles.progressNumber}>
                {stats.optionalCompleted}/{stats.optionalTotal}
              </Text>
              <Text style={styles.progressLabel}>Opcionales</Text>
            </View>
          </View>
        </View>

        {/* Consentimiento */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Consentimiento Informado</Text>
          <TouchableOpacity 
            style={styles.consentItem}
            onPress={() => setConsentObtained(!consentObtained)}
          >
            <Switch
              value={consentObtained}
              onValueChange={setConsentObtained}
              trackColor={{ false: COLORS.gray300, true: COLORS.success + '40' }}
              thumbColor={consentObtained ? COLORS.success : COLORS.gray400}
            />
            <Text style={styles.consentText}>
              El cliente ha leído, entendido y firmado el consentimiento informado
            </Text>
          </TouchableOpacity>
        </View>

        {/* Test de parche */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test de Parche</Text>
          
          <TouchableOpacity 
            style={styles.patchTestItem}
            onPress={() => handlePatchTestToggle(!patchTestRequired)}
          >
            <Switch
              value={patchTestRequired}
              onValueChange={handlePatchTestToggle}
              trackColor={{ false: COLORS.gray300, true: COLORS.warning + '40' }}
              thumbColor={patchTestRequired ? COLORS.warning : COLORS.gray400}
            />
            <Text style={styles.patchTestText}>
              Se requiere test de parche para este cliente
            </Text>
          </TouchableOpacity>

          {patchTestRequired && (
            <View style={styles.patchTestDetails}>
              <TouchableOpacity 
                style={styles.patchTestCompleted}
                onPress={() => setPatchTestCompleted(!patchTestCompleted)}
              >
                <Switch
                  value={patchTestCompleted}
                  onValueChange={setPatchTestCompleted}
                  trackColor={{ false: COLORS.gray300, true: COLORS.info + '40' }}
                  thumbColor={patchTestCompleted ? COLORS.info : COLORS.gray400}
                />
                <Text style={styles.patchTestCompletedText}>
                  Test de parche completado
                </Text>
              </TouchableOpacity>

              {patchTestCompleted && (
                <View style={styles.patchTestResults}>
                  <Text style={styles.patchTestResultsTitle}>Resultado:</Text>
                  <View style={styles.patchTestOptions}>
                    {['negative', 'positive'].map((result) => (
                      <TouchableOpacity
                        key={result}
                        style={[
                          styles.patchTestOption,
                          patchTestResult === result && styles.patchTestOptionSelected,
                          result === 'positive' && patchTestResult === result && styles.patchTestOptionDanger,
                        ]}
                        onPress={() => setPatchTestResult(result as any)}
                      >
                        <Text style={[
                          styles.patchTestOptionText,
                          patchTestResult === result && styles.patchTestOptionTextSelected,
                        ]}>
                          {result === 'negative' ? 'Negativo ✓' : 'Positivo ⚠️'}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Checklist de seguridad */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lista de Verificación</Text>
          {checklist.map((item) => (
            <ChecklistItem key={item.id} item={item} />
          ))}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={20} 
            color={COLORS.textSecondary} 
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[
            styles.continueButton,
            !stats.allRequiredCompleted && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={!stats.allRequiredCompleted}
        >
          <Text style={styles.continueButtonText}>
            {stats.allRequiredCompleted ? 'Continuar' : `Faltan ${stats.requiredTotal - stats.requiredCompleted} obligatorios`}
          </Text>
          <MaterialCommunityIcons 
            name="arrow-right" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    paddingVertical: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
  },
  clientInfo: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  clientName: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  allergiesAlert: {
    flexDirection: 'row',
    backgroundColor: COLORS.warning + '10',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    gap: SPACING.sm,
  },
  allergiesContent: {
    flex: 1,
  },
  allergiesTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  allergyItem: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.warning,
    marginBottom: 2,
  },
  progressSection: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  progressTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  progressStats: {
    flexDirection: 'row',
    gap: SPACING.lg,
  },
  progressItem: {
    alignItems: 'center',
  },
  progressNumber: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.primary,
  },
  progressLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  section: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  consentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  consentText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    lineHeight: 20,
  },
  patchTestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
    marginBottom: SPACING.md,
  },
  patchTestText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    lineHeight: 20,
  },
  patchTestDetails: {
    paddingLeft: SPACING.lg,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.warning + '30',
  },
  patchTestCompleted: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
    marginBottom: SPACING.md,
  },
  patchTestCompletedText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
  },
  patchTestResults: {
    marginTop: SPACING.sm,
  },
  patchTestResultsTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  patchTestOptions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  patchTestOption: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray100,
    alignItems: 'center',
  },
  patchTestOptionSelected: {
    backgroundColor: COLORS.success,
  },
  patchTestOptionDanger: {
    backgroundColor: COLORS.error,
  },
  patchTestOptionText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  patchTestOptionTextSelected: {
    color: COLORS.textInverse,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: SPACING.sm,
    gap: SPACING.md,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: COLORS.gray300,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkboxCompleted: {
    backgroundColor: COLORS.success,
    borderColor: COLORS.success,
  },
  checkboxRequired: {
    borderColor: COLORS.warning,
  },
  checklistContent: {
    flex: 1,
  },
  checklistText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    lineHeight: 20,
  },
  checklistTextCompleted: {
    textDecorationLine: 'line-through',
    color: COLORS.textSecondary,
  },
  requiredLabel: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.warning,
    fontWeight: TYPOGRAPHY.weights.medium,
    marginTop: 2,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonDisabled: {
    backgroundColor: COLORS.gray300,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
    textAlign: 'center',
  },
});

export default SafetyProtocolStep;
