import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { ConsultationFlow, ConsultationFlowData, AIAnalysisResults, AIRecommendation, HairZoneAnalysis } from '../../../types';
import ZoneAnalysisDisplay from '../../../components/consultation/ZoneAnalysisDisplay';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS, HAIR_LEVELS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AIAnalysisStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [analysisPhase, setAnalysisPhase] = useState<'analyzing' | 'completed'>('analyzing');
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResults | null>(null);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [progressAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    startAnalysis();
  }, []);

  const startAnalysis = async () => {
    setIsLoading(true);
    setAnalysisPhase('analyzing');

    // Animar progreso
    Animated.timing(progressAnimation, {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();

    // Simular análisis IA con delay realista
    setTimeout(() => {
      generateMockAnalysis();
    }, 3500);
  };

  const generateMockAnalysis = () => {
    // Generar análisis mock realista basado en las imágenes capturadas
    const capturedImages = consultationFlow.data.imageCapture?.capturedImages || [];
    const hasCloseUpRoots = capturedImages.some(img => img.angle === 'close-up-roots');
    const hasCloseUpEnds = capturedImages.some(img => img.angle === 'close-up-ends');
    const hasMultipleAngles = capturedImages.length >= 3;

    // Análisis base con variación realista
    const baseLevel = Math.floor(Math.random() * 4) + 3; // Niveles 3-6 más comunes
    const grayPercentage = Math.floor(Math.random() * 60); // 0-60% canas
    const confidence = hasMultipleAngles ? 0.85 + Math.random() * 0.1 : 0.7 + Math.random() * 0.15;

    // Generar análisis por zonas
    const generateZoneAnalysis = (zone: 'roots' | 'mids' | 'ends'): HairZoneAnalysis => {
      const zoneConfidence = zone === 'roots' && hasCloseUpRoots ? confidence + 0.1 :
                            zone === 'ends' && hasCloseUpEnds ? confidence + 0.1 :
                            confidence - 0.1;

      const hasArtificialColor = Math.random() > 0.4; // 60% probabilidad de color artificial
      const levelVariation = Math.floor(Math.random() * 3) - 1; // -1, 0, +1 variación por zona

      return {
        zone,
        naturalLevel: {
          value: Math.max(1, Math.min(10, baseLevel + levelVariation)),
          confidence: Math.min(1, zoneConfidence),
          description: `Nivel natural detectado en ${zone === 'roots' ? 'raíces' : zone === 'mids' ? 'medios' : 'puntas'}`,
        },
        artificialColor: {
          detected: hasArtificialColor && zone !== 'roots',
          level: hasArtificialColor ? baseLevel + Math.floor(Math.random() * 3) + 1 : undefined,
          tone: hasArtificialColor ? ['Dorado', 'Cenizo', 'Cobrizo', 'Natural'][Math.floor(Math.random() * 4)] : undefined,
          brand: hasArtificialColor ? ['Wella', 'L\'Oréal', 'Matrix'][Math.floor(Math.random() * 3)] : undefined,
          ageWeeks: hasArtificialColor ? Math.floor(Math.random() * 12) + 2 : undefined,
          fadeLevel: hasArtificialColor ? ['minimal', 'moderate', 'significant'][Math.floor(Math.random() * 3)] as any : undefined,
        },
        undertone: {
          value: Math.random() > 0.6 ? 'warm' : Math.random() > 0.5 ? 'cool' : 'neutral',
          confidence: zoneConfidence - 0.1,
          description: 'Subtono detectado mediante análisis espectral',
        },
        porosity: {
          value: zone === 'ends' ?
            (Math.random() > 0.3 ? 'high' : 'medium') : // Puntas más porosas
            (Math.random() > 0.6 ? 'medium' : Math.random() > 0.3 ? 'low' : 'high'),
          confidence: zoneConfidence,
          description: 'Capacidad de absorción del cabello',
          uniformity: Math.random() > 0.7 ? 'uneven' : 'even',
        },
        elasticity: {
          value: zone === 'ends' ?
            (Math.random() > 0.4 ? 'poor' : 'normal') : // Puntas más dañadas
            (Math.random() > 0.7 ? 'excellent' : Math.random() > 0.4 ? 'normal' : 'poor'),
          confidence: zoneConfidence - 0.1,
          stretchTest: Math.random() > 0.5, // 50% probabilidad de test físico
        },
        condition: {
          damage: zone === 'ends' ?
            (Math.random() > 0.3 ? 'moderate' : Math.random() > 0.6 ? 'minimal' : 'severe') :
            (Math.random() > 0.6 ? 'none' : Math.random() > 0.4 ? 'minimal' : 'moderate'),
          dryness: zone === 'ends' ?
            (Math.random() > 0.4 ? 'moderate' : 'mild') :
            (Math.random() > 0.6 ? 'none' : Math.random() > 0.4 ? 'mild' : 'moderate'),
          breakage: zone === 'ends' ?
            (Math.random() > 0.5 ? 'minimal' : 'moderate') :
            (Math.random() > 0.7 ? 'none' : 'minimal'),
          chemicalResidues: hasArtificialColor && Math.random() > 0.6,
          metalDeposits: Math.random() > 0.8, // 20% probabilidad
        },
        // Información específica por zona
        ...(zone === 'roots' && {
          rootsSpecific: {
            growthLength: Math.floor(Math.random() * 30) + 5, // 5-35mm
            grayPercentage,
            grayDistribution: grayPercentage > 40 ? 'even' : grayPercentage > 20 ? 'scattered' : 'concentrated',
            naturalTexture: ['straight', 'wavy', 'curly', 'coily'][Math.floor(Math.random() * 4)] as any,
          },
        }),
        ...(zone === 'mids' && {
          midsSpecific: {
            colorUniformity: hasArtificialColor ?
              (Math.random() > 0.6 ? 'even' : Math.random() > 0.3 ? 'banded' : 'patchy') : 'even',
            previousTreatments: hasArtificialColor ?
              ['Coloración', 'Mechas', 'Balayage'].filter(() => Math.random() > 0.5) : [],
            oxidationLevel: hasArtificialColor ?
              (Math.random() > 0.5 ? 'moderate' : 'minimal') : 'none',
          },
        }),
        ...(zone === 'ends' && {
          endsSpecific: {
            mechanicalDamage: Math.random() > 0.4 ? 'moderate' : Math.random() > 0.7 ? 'minimal' : 'severe',
            splitEnds: Math.random() > 0.3 ? 'moderate' : Math.random() > 0.6 ? 'minimal' : 'severe',
            porosityVariation: Math.random() > 0.5,
            needsTrimming: Math.random() > 0.4,
          },
        }),
      };
    };

    const zoneAnalysis = [
      generateZoneAnalysis('roots'),
      generateZoneAnalysis('mids'),
      generateZoneAnalysis('ends'),
    ];

    // Calcular análisis de uniformidad entre zonas
    const levelVariation = Math.max(...zoneAnalysis.map(z => z.naturalLevel.value)) -
                          Math.min(...zoneAnalysis.map(z => z.naturalLevel.value));
    const toneVariation = new Set(zoneAnalysis.map(z => z.undertone.value)).size > 1;
    const porosityVariation = new Set(zoneAnalysis.map(z => z.porosity.value)).size > 1;

    // Detectar historial químico general
    const hasAnyArtificialColor = zoneAnalysis.some(z => z.artificialColor.detected);
    const hasChemicalResidues = zoneAnalysis.some(z => z.condition.chemicalResidues);
    const hasMetalDeposits = zoneAnalysis.some(z => z.condition.metalDeposits);

    const mockResults: AIAnalysisResults = {
      overallAssessment: {
        hairType: hasAnyArtificialColor ? 'color-treated' :
                 zoneAnalysis.some(z => z.condition.damage !== 'none') ? 'chemically-processed' :
                 zoneAnalysis.some(z => z.condition.damage === 'severe') ? 'damaged' : 'virgin',
        complexity: levelVariation > 2 || toneVariation || porosityVariation ? 'complex' :
                   hasAnyArtificialColor ? 'moderate' : 'simple',
        recommendedApproach: levelVariation > 2 ? 'Tratamiento por zonas diferenciado' :
                           hasAnyArtificialColor ? 'Considerar historial de color' :
                           'Proceso estándar aplicable',
      },
      zoneAnalysis,
      diameter: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.3 ? 'fine' : 'coarse',
        confidence: confidence - 0.15,
        description: 'Grosor de la fibra capilar',
      },
      density: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.3 ? 'high' : 'low',
        confidence: confidence - 0.1,
        description: 'Cantidad de cabello por cm²',
      },
      uniformityAnalysis: {
        levelVariation,
        toneVariation,
        porosityVariation,
        recommendations: [
          ...(levelVariation > 2 ? ['Aplicar técnica de pre-pigmentación en zonas más claras'] : []),
          ...(toneVariation ? ['Neutralizar subtonos no deseados antes de aplicar color'] : []),
          ...(porosityVariation ? ['Usar relleno de porosidad en zonas más dañadas'] : []),
        ],
      },
      chemicalHistory: {
        previousColor: hasAnyArtificialColor,
        bleachHistory: zoneAnalysis.some(z => z.artificialColor.detected && z.artificialColor.level && z.artificialColor.level > 7),
        permanentWave: Math.random() > 0.8, // 20% probabilidad
        relaxer: Math.random() > 0.9, // 10% probabilidad
        estimatedLastService: hasAnyArtificialColor ?
          `${Math.floor(Math.random() * 12) + 2}-${Math.floor(Math.random() * 4) + 2} semanas` :
          'Sin servicios químicos recientes',
        compatibilityWarnings: [
          ...(hasChemicalResidues ? ['Residuos químicos detectados - realizar test de compatibilidad'] : []),
          ...(hasMetalDeposits ? ['Depósitos metálicos - usar quelante antes del servicio'] : []),
          ...(zoneAnalysis.some(z => z.condition.damage === 'severe') ? ['Cabello severamente dañado - considerar tratamiento reconstructor'] : []),
        ],
      },
    };

    // Generar recomendaciones basadas en el análisis por zonas
    const mockRecommendations: AIRecommendation[] = [];

    // Recomendaciones por variación entre zonas
    if (levelVariation > 2) {
      mockRecommendations.push({
        id: 'zone-variation',
        type: 'technique',
        priority: 'high',
        title: 'Tratamiento por Zonas',
        description: 'Aplicar técnica diferenciada debido a variación significativa entre zonas',
        reasoning: `Diferencia de ${levelVariation} niveles entre raíces, medios y puntas`,
        actionRequired: true,
      });
    }

    // Recomendaciones por canas
    const rootsData = zoneAnalysis.find(z => z.zone === 'roots');
    if (rootsData?.rootsSpecific && rootsData.rootsSpecific.grayPercentage > 50) {
      mockRecommendations.push({
        id: 'gray-coverage',
        type: 'technique',
        priority: 'high',
        title: 'Cobertura de Canas',
        description: 'Usar productos específicos para cobertura de canas y pre-pigmentación',
        reasoning: `${rootsData.rootsSpecific.grayPercentage}% de canas con distribución ${rootsData.rootsSpecific.grayDistribution}`,
        actionRequired: true,
      });
    }

    // Recomendaciones por historial químico
    if (hasChemicalResidues || hasMetalDeposits) {
      mockRecommendations.push({
        id: 'chemical-safety',
        type: 'safety',
        priority: 'high',
        title: 'Test de Compatibilidad',
        description: 'Realizar test obligatorio antes del servicio',
        reasoning: hasChemicalResidues ? 'Residuos químicos detectados' : 'Depósitos metálicos detectados',
        actionRequired: true,
      });
    }

    // Recomendaciones por porosidad variable
    if (porosityVariation) {
      mockRecommendations.push({
        id: 'porosity-treatment',
        type: 'product',
        priority: 'medium',
        title: 'Relleno de Porosidad',
        description: 'Aplicar tratamiento de relleno en zonas más porosas antes de colorar',
        reasoning: 'Porosidad desigual entre zonas puede causar absorción irregular',
        actionRequired: true,
      });
    }

    // Recomendaciones por estado de puntas
    const endsData = zoneAnalysis.find(z => z.zone === 'ends');
    if (endsData?.endsSpecific?.needsTrimming) {
      mockRecommendations.push({
        id: 'trim-ends',
        type: 'technique',
        priority: 'medium',
        title: 'Corte de Puntas',
        description: 'Recortar puntas antes del servicio para mejor resultado',
        reasoning: 'Puntas dañadas pueden afectar la uniformidad del color',
        actionRequired: false,
      });
    }

    // Recomendaciones por daño severo
    if (zoneAnalysis.some(z => z.condition.damage === 'severe')) {
      mockRecommendations.push({
        id: 'damage-treatment',
        type: 'safety',
        priority: 'high',
        title: 'Tratamiento Reconstructor',
        description: 'Aplicar tratamiento reconstructor antes de cualquier proceso químico',
        reasoning: 'Daño severo detectado que requiere reparación previa',
        actionRequired: true,
      });
    }

    setAnalysisResults(mockResults);
    setRecommendations(mockRecommendations);
    setAnalysisPhase('completed');
    setIsLoading(false);
  };

  const getAverageConfidence = (): number => {
    if (!analysisResults?.zoneAnalysis || analysisResults.zoneAnalysis.length === 0) return 0;
    return analysisResults.zoneAnalysis.reduce((sum, zone) =>
      sum + zone.naturalLevel.confidence, 0) / analysisResults.zoneAnalysis.length;
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return COLORS.success;
    if (confidence >= 0.6) return COLORS.warning;
    return COLORS.error;
  };

  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return 'Alta confianza';
    if (confidence >= 0.6) return 'Confianza media';
    return 'Baja confianza';
  };

  const getPriorityColor = (priority: AIRecommendation['priority']): string => {
    switch (priority) {
      case 'critical': return COLORS.error;
      case 'high': return COLORS.warning;
      case 'medium': return COLORS.info;
      case 'low': return COLORS.gray400;
    }
  };

  const handleContinue = () => {
    if (!analysisResults) return;

    // Calcular confianza promedio de todas las zonas
    const averageConfidence = analysisResults.zoneAnalysis && analysisResults.zoneAnalysis.length > 0
      ? analysisResults.zoneAnalysis.reduce((sum, zone) =>
          sum + zone.naturalLevel.confidence, 0) / analysisResults.zoneAnalysis.length
      : 0.8; // Valor por defecto

    // Guardar datos del análisis IA
    updateFlowData({
      aiAnalysis: {
        analysisId: `analysis-${Date.now()}`,
        processedAt: new Date().toISOString(),
        processingTime: 3500,
        confidence: averageConfidence,
        results: analysisResults,
        recommendations,
      },
    });

    goToNextStep();
  };

  if (analysisPhase === 'analyzing') {
    return (
      <View style={styles.analyzingContainer}>
        <View style={styles.analyzingContent}>
          <View style={styles.brainIcon}>
            <MaterialCommunityIcons
              name="brain"
              size={64}
              color={COLORS.primary}
            />
          </View>

          <Text style={styles.analyzingTitle}>Analizando con IA</Text>
          <Text style={styles.analyzingSubtitle}>
            Nuestro motor de IA está procesando las imágenes del cabello...
          </Text>

          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>

          <View style={styles.analysisSteps}>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="image-search" size={20} color={COLORS.success} />
              <Text style={styles.stepText}>Procesando imágenes</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="palette" size={20} color={COLORS.success} />
              <Text style={styles.stepText}>Analizando color natural</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="microscope" size={20} color={COLORS.warning} />
              <Text style={styles.stepText}>Evaluando estructura</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="lightbulb" size={20} color={COLORS.gray400} />
              <Text style={styles.stepText}>Generando recomendaciones</Text>
            </View>
          </View>

          <ActivityIndicator size="large" color={COLORS.primary} style={styles.spinner} />
        </View>
      </View>
    );
  }

  // Mostrar resultados del análisis
  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerIcon}>
            <MaterialCommunityIcons
              name="check-circle"
              size={32}
              color={COLORS.success}
            />
          </View>
          <Text style={styles.title}>Análisis Completado</Text>
          <Text style={styles.subtitle}>
            IA ha analizado {consultationFlow.data.imageCapture?.capturedImages.length || 0} imágenes
          </Text>
        </View>

        {/* Confianza general */}
        <View style={styles.confidenceSection}>
          <Text style={styles.confidenceTitle}>Confianza del análisis</Text>
          <View style={styles.confidenceBar}>
            <View
              style={[
                styles.confidenceFill,
                {
                  width: `${getAverageConfidence() * 100}%`,
                  backgroundColor: getConfidenceColor(getAverageConfidence()),
                }
              ]}
            />
          </View>
          <Text style={styles.confidenceText}>
            {getConfidenceText(getAverageConfidence())}
            ({Math.round(getAverageConfidence() * 100)}%)
          </Text>
        </View>

        {/* Resultados principales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Análisis Principal</Text>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="numeric" size={24} color={COLORS.primary} />
              <Text style={styles.resultTitle}>Nivel Natural</Text>
            </View>
            <Text style={styles.resultValue}>
              Análisis por zonas completado
            </Text>
            <Text style={styles.resultConfidence}>
              {analysisResults.zoneAnalysis.length} zonas analizadas
            </Text>
          </View>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="palette" size={24} color={COLORS.secondary} />
              <Text style={styles.resultTitle}>Subtono</Text>
            </View>
            <Text style={styles.resultValue}>
              Análisis de subtonos por zona
            </Text>
            <Text style={styles.resultDescription}>
              Información detallada disponible en validación
            </Text>
          </View>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="hair-dryer" size={24} color={COLORS.accent} />
              <Text style={styles.resultTitle}>Canas</Text>
            </View>
            <Text style={styles.resultValue}>
              Información específica por zona
            </Text>
          </View>
        </View>

        {/* Características del cabello */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Características del Cabello</Text>

          <View style={styles.characteristicsGrid}>
            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Diámetro</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.diameter?.value === 'fine' ? 'Fino' :
                 analysisResults?.diameter?.value === 'medium' ? 'Medio' :
                 analysisResults?.diameter?.value === 'coarse' ? 'Grueso' : 'N/A'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Densidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.density?.value === 'low' ? 'Baja' :
                 analysisResults?.density?.value === 'medium' ? 'Media' :
                 analysisResults?.density?.value === 'high' ? 'Alta' : 'N/A'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Complejidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.overallAssessment?.complexity === 'simple' ? 'Simple' :
                 analysisResults?.overallAssessment?.complexity === 'moderate' ? 'Moderada' :
                 analysisResults?.overallAssessment?.complexity === 'complex' ? 'Compleja' : 'Alto riesgo'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Tipo</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.overallAssessment?.hairType === 'virgin' ? 'Virgen' :
                 analysisResults?.overallAssessment?.hairType === 'color-treated' ? 'Con color' :
                 analysisResults?.overallAssessment?.hairType === 'chemically-processed' ? 'Procesado' : 'Dañado'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Zonas</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.zoneAnalysis?.length || 0} analizadas
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Uniformidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.uniformityAnalysis?.levelVariation ?
                  `${analysisResults.uniformityAnalysis.levelVariation} niveles` : 'Uniforme'}
              </Text>
            </View>
          </View>
        </View>

        {/* Recomendaciones */}
        {recommendations.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recomendaciones de IA</Text>
            {recommendations.map((recommendation) => (
              <View key={recommendation.id} style={styles.recommendationCard}>
                <View style={styles.recommendationHeader}>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: getPriorityColor(recommendation.priority) + '20' }
                  ]}>
                    <Text style={[
                      styles.priorityText,
                      { color: getPriorityColor(recommendation.priority) }
                    ]}>
                      {recommendation.priority === 'critical' ? 'Crítico' :
                       recommendation.priority === 'high' ? 'Alto' :
                       recommendation.priority === 'medium' ? 'Medio' : 'Bajo'}
                    </Text>
                  </View>
                  {recommendation.actionRequired && (
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={16}
                      color={COLORS.warning}
                    />
                  )}
                </View>
                <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
                <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
                <Text style={styles.recommendationReasoning}>{recommendation.reasoning}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons
            name="arrow-left"
            size={20}
            color={COLORS.textSecondary}
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
        >
          <Text style={styles.continueButtonText}>Definir Color Objetivo</Text>
          <MaterialCommunityIcons
            name="palette"
            size={20}
            color={COLORS.textInverse}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  analyzingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  analyzingContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  brainIcon: {
    marginBottom: SPACING.lg,
  },
  analyzingTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  analyzingSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  progressContainer: {
    width: '100%',
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.xl,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
  },
  analysisSteps: {
    width: '100%',
    marginBottom: SPACING.xl,
  },
  analysisStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  stepText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  spinner: {
    marginTop: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  headerIcon: {
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  confidenceSection: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  confidenceTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  confidenceBar: {
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.sm,
    overflow: 'hidden',
  },
  confidenceFill: {
    height: '100%',
  },
  confidenceText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  resultCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  resultTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  resultValue: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  resultDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  resultConfidence: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textTertiary,
  },
  characteristicsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  characteristicItem: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    width: '47%',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  characteristicLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  characteristicValue: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  recommendationCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  priorityBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  priorityText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.semibold,
    textTransform: 'uppercase',
  },
  recommendationTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  recommendationDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    lineHeight: 20,
  },
  recommendationReasoning: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    lineHeight: 18,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default AIAnalysisStep;