import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { ConsultationFlow, ConsultationFlowData, AIAnalysisResults, AIRecommendation } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS, HAIR_LEVELS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AIAnalysisStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [analysisPhase, setAnalysisPhase] = useState<'analyzing' | 'completed'>('analyzing');
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResults | null>(null);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [progressAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    startAnalysis();
  }, []);

  const startAnalysis = async () => {
    setIsLoading(true);
    setAnalysisPhase('analyzing');

    // Animar progreso
    Animated.timing(progressAnimation, {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();

    // Simular análisis IA con delay realista
    setTimeout(() => {
      generateMockAnalysis();
    }, 3500);
  };

  const generateMockAnalysis = () => {
    // Generar análisis mock realista basado en las imágenes capturadas
    const capturedImages = consultationFlow.data.imageCapture?.capturedImages || [];
    const hasCloseUpRoots = capturedImages.some(img => img.angle === 'close-up-roots');
    const hasMultipleAngles = capturedImages.length >= 3;

    // Análisis base con variación realista
    const baseLevel = Math.floor(Math.random() * 4) + 3; // Niveles 3-6 más comunes
    const grayPercentage = Math.floor(Math.random() * 60); // 0-60% canas
    const confidence = hasMultipleAngles ? 0.85 + Math.random() * 0.1 : 0.7 + Math.random() * 0.15;

    const mockResults: AIAnalysisResults = {
      naturalLevel: {
        value: baseLevel,
        confidence: confidence,
        description: HAIR_LEVELS.find(level => level.level === baseLevel)?.name || 'Nivel desconocido',
      },
      undertone: {
        value: Math.random() > 0.6 ? 'warm' : Math.random() > 0.5 ? 'cool' : 'neutral',
        confidence: confidence - 0.1,
        description: 'Subtono detectado mediante análisis espectral',
      },
      grayPercentage: {
        value: grayPercentage,
        confidence: hasCloseUpRoots ? confidence : confidence - 0.2,
        distribution: grayPercentage > 40 ? 'even' : grayPercentage > 20 ? 'scattered' : 'concentrated',
      },
      diameter: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.5 ? 'fine' : 'coarse',
        confidence: confidence - 0.15,
        description: 'Grosor del cabello analizado por densidad visual',
      },
      density: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.5 ? 'high' : 'low',
        confidence: confidence - 0.1,
        description: 'Cantidad de cabello por cm²',
      },
      porosity: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.5 ? 'high' : 'low',
        confidence: confidence - 0.2,
        description: 'Capacidad de absorción del cabello',
      },
      elasticity: {
        value: Math.random() > 0.7 ? 'excellent' : Math.random() > 0.4 ? 'normal' : 'poor',
        confidence: confidence - 0.25,
        description: 'Resistencia al estiramiento',
      },
      resistance: {
        value: Math.random() > 0.6 ? 'medium' : Math.random() > 0.5 ? 'high' : 'low',
        confidence: confidence - 0.15,
        description: 'Resistencia a procesos químicos',
      },
      condition: {
        overall: Math.random() > 0.7 ? 'excellent' : Math.random() > 0.4 ? 'good' : Math.random() > 0.2 ? 'fair' : 'poor',
        damage: Math.random() > 0.6 ? 'none' : Math.random() > 0.4 ? 'minimal' : Math.random() > 0.2 ? 'moderate' : 'severe',
        dryness: Math.random() > 0.6 ? 'none' : Math.random() > 0.4 ? 'mild' : Math.random() > 0.2 ? 'moderate' : 'severe',
        breakage: Math.random() > 0.7 ? 'none' : Math.random() > 0.5 ? 'minimal' : Math.random() > 0.3 ? 'moderate' : 'severe',
      },
      existingColor: {
        detected: Math.random() > 0.4,
        type: Math.random() > 0.6 ? 'colored' : Math.random() > 0.4 ? 'highlighted' : 'virgin',
        level: baseLevel + Math.floor(Math.random() * 3) - 1,
        tone: 'Tonos dorados detectados',
        fadeLevel: Math.random() > 0.5 ? 'moderate' : 'minimal',
      },
    };

    // Generar recomendaciones basadas en el análisis
    const mockRecommendations: AIRecommendation[] = [];

    // Recomendaciones de seguridad
    if (grayPercentage > 50) {
      mockRecommendations.push({
        id: 'gray-coverage',
        type: 'technique',
        priority: 'high',
        title: 'Cobertura de canas',
        description: 'Se recomienda usar productos específicos para cobertura de canas',
        reasoning: `Con ${grayPercentage}% de canas, se necesita formulación especializada`,
        actionRequired: true,
      });
    }

    if (mockResults.condition.damage !== 'none') {
      mockRecommendations.push({
        id: 'hair-treatment',
        type: 'safety',
        priority: 'medium',
        title: 'Tratamiento previo',
        description: 'Considerar tratamiento reconstructor antes de la coloración',
        reasoning: 'El cabello muestra signos de daño que podrían afectar el resultado',
        actionRequired: false,
      });
    }

    if (mockResults.porosity.value === 'high') {
      mockRecommendations.push({
        id: 'porosity-adjustment',
        type: 'technique',
        priority: 'medium',
        title: 'Ajuste por porosidad',
        description: 'Reducir tiempo de procesamiento debido a alta porosidad',
        reasoning: 'El cabello poroso absorbe color más rápidamente',
        actionRequired: true,
      });
    }

    // Recomendaciones de producto
    if (mockResults.resistance.value === 'high') {
      mockRecommendations.push({
        id: 'processing-time',
        type: 'timing',
        priority: 'medium',
        title: 'Tiempo extendido',
        description: 'Aumentar tiempo de procesamiento en 10-15 minutos',
        reasoning: 'Cabello resistente requiere más tiempo para penetración del color',
        actionRequired: true,
      });
    }

    setAnalysisResults(mockResults);
    setRecommendations(mockRecommendations);
    setAnalysisPhase('completed');
    setIsLoading(false);
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return COLORS.success;
    if (confidence >= 0.6) return COLORS.warning;
    return COLORS.error;
  };

  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return 'Alta confianza';
    if (confidence >= 0.6) return 'Confianza media';
    return 'Baja confianza';
  };

  const getPriorityColor = (priority: AIRecommendation['priority']): string => {
    switch (priority) {
      case 'critical': return COLORS.error;
      case 'high': return COLORS.warning;
      case 'medium': return COLORS.info;
      case 'low': return COLORS.gray400;
    }
  };

  const handleContinue = () => {
    if (!analysisResults) return;

    // Guardar datos del análisis IA
    updateFlowData({
      aiAnalysis: {
        analysisId: `analysis-${Date.now()}`,
        processedAt: new Date().toISOString(),
        processingTime: 3500,
        confidence: analysisResults.naturalLevel.confidence,
        results: analysisResults,
        recommendations,
      },
    });

    goToNextStep();
  };

  if (analysisPhase === 'analyzing') {
    return (
      <View style={styles.analyzingContainer}>
        <View style={styles.analyzingContent}>
          <View style={styles.brainIcon}>
            <MaterialCommunityIcons
              name="brain"
              size={64}
              color={COLORS.primary}
            />
          </View>

          <Text style={styles.analyzingTitle}>Analizando con IA</Text>
          <Text style={styles.analyzingSubtitle}>
            Nuestro motor de IA está procesando las imágenes del cabello...
          </Text>

          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>

          <View style={styles.analysisSteps}>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="image-search" size={20} color={COLORS.success} />
              <Text style={styles.stepText}>Procesando imágenes</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="palette" size={20} color={COLORS.success} />
              <Text style={styles.stepText}>Analizando color natural</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="microscope" size={20} color={COLORS.warning} />
              <Text style={styles.stepText}>Evaluando estructura</Text>
            </View>
            <View style={styles.analysisStep}>
              <MaterialCommunityIcons name="lightbulb" size={20} color={COLORS.gray400} />
              <Text style={styles.stepText}>Generando recomendaciones</Text>
            </View>
          </View>

          <ActivityIndicator size="large" color={COLORS.primary} style={styles.spinner} />
        </View>
      </View>
    );
  }

  // Mostrar resultados del análisis
  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerIcon}>
            <MaterialCommunityIcons
              name="check-circle"
              size={32}
              color={COLORS.success}
            />
          </View>
          <Text style={styles.title}>Análisis Completado</Text>
          <Text style={styles.subtitle}>
            IA ha analizado {consultationFlow.data.imageCapture?.capturedImages.length || 0} imágenes
          </Text>
        </View>

        {/* Confianza general */}
        <View style={styles.confidenceSection}>
          <Text style={styles.confidenceTitle}>Confianza del análisis</Text>
          <View style={styles.confidenceBar}>
            <View
              style={[
                styles.confidenceFill,
                {
                  width: `${(analysisResults?.naturalLevel.confidence || 0) * 100}%`,
                  backgroundColor: getConfidenceColor(analysisResults?.naturalLevel.confidence || 0),
                }
              ]}
            />
          </View>
          <Text style={styles.confidenceText}>
            {getConfidenceText(analysisResults?.naturalLevel.confidence || 0)}
            ({Math.round((analysisResults?.naturalLevel.confidence || 0) * 100)}%)
          </Text>
        </View>

        {/* Resultados principales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Análisis Principal</Text>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="numeric" size={24} color={COLORS.primary} />
              <Text style={styles.resultTitle}>Nivel Natural</Text>
            </View>
            <Text style={styles.resultValue}>
              Nivel {analysisResults?.naturalLevel.value} - {analysisResults?.naturalLevel.description}
            </Text>
            <Text style={styles.resultConfidence}>
              Confianza: {Math.round((analysisResults?.naturalLevel.confidence || 0) * 100)}%
            </Text>
          </View>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="palette" size={24} color={COLORS.secondary} />
              <Text style={styles.resultTitle}>Subtono</Text>
            </View>
            <Text style={styles.resultValue}>
              {analysisResults?.undertone.value === 'warm' ? 'Cálido' :
               analysisResults?.undertone.value === 'cool' ? 'Frío' : 'Neutro'}
            </Text>
            <Text style={styles.resultDescription}>
              {analysisResults?.undertone.description}
            </Text>
          </View>

          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <MaterialCommunityIcons name="hair-dryer" size={24} color={COLORS.accent} />
              <Text style={styles.resultTitle}>Canas</Text>
            </View>
            <Text style={styles.resultValue}>
              {analysisResults?.grayPercentage.value}% - Distribución {analysisResults?.grayPercentage.distribution}
            </Text>
          </View>
        </View>

        {/* Características del cabello */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Características del Cabello</Text>

          <View style={styles.characteristicsGrid}>
            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Diámetro</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.diameter.value === 'fine' ? 'Fino' :
                 analysisResults?.diameter.value === 'medium' ? 'Medio' : 'Grueso'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Densidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.density.value === 'low' ? 'Baja' :
                 analysisResults?.density.value === 'medium' ? 'Media' : 'Alta'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Porosidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.porosity.value === 'low' ? 'Baja' :
                 analysisResults?.porosity.value === 'medium' ? 'Media' : 'Alta'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Elasticidad</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.elasticity.value === 'poor' ? 'Pobre' :
                 analysisResults?.elasticity.value === 'normal' ? 'Normal' : 'Excelente'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Resistencia</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.resistance.value === 'low' ? 'Baja' :
                 analysisResults?.resistance.value === 'medium' ? 'Media' : 'Alta'}
              </Text>
            </View>

            <View style={styles.characteristicItem}>
              <Text style={styles.characteristicLabel}>Condición</Text>
              <Text style={styles.characteristicValue}>
                {analysisResults?.condition.overall === 'poor' ? 'Pobre' :
                 analysisResults?.condition.overall === 'fair' ? 'Regular' :
                 analysisResults?.condition.overall === 'good' ? 'Buena' : 'Excelente'}
              </Text>
            </View>
          </View>
        </View>

        {/* Recomendaciones */}
        {recommendations.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recomendaciones de IA</Text>
            {recommendations.map((recommendation) => (
              <View key={recommendation.id} style={styles.recommendationCard}>
                <View style={styles.recommendationHeader}>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: getPriorityColor(recommendation.priority) + '20' }
                  ]}>
                    <Text style={[
                      styles.priorityText,
                      { color: getPriorityColor(recommendation.priority) }
                    ]}>
                      {recommendation.priority === 'critical' ? 'Crítico' :
                       recommendation.priority === 'high' ? 'Alto' :
                       recommendation.priority === 'medium' ? 'Medio' : 'Bajo'}
                    </Text>
                  </View>
                  {recommendation.actionRequired && (
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={16}
                      color={COLORS.warning}
                    />
                  )}
                </View>
                <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
                <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
                <Text style={styles.recommendationReasoning}>{recommendation.reasoning}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons
            name="arrow-left"
            size={20}
            color={COLORS.textSecondary}
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
        >
          <Text style={styles.continueButtonText}>Definir Color Objetivo</Text>
          <MaterialCommunityIcons
            name="palette"
            size={20}
            color={COLORS.textInverse}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  analyzingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  analyzingContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  brainIcon: {
    marginBottom: SPACING.lg,
  },
  analyzingTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  analyzingSubtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  progressContainer: {
    width: '100%',
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.xl,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
  },
  analysisSteps: {
    width: '100%',
    marginBottom: SPACING.xl,
  },
  analysisStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  stepText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  spinner: {
    marginTop: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  headerIcon: {
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  confidenceSection: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  confidenceTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  confidenceBar: {
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    marginBottom: SPACING.sm,
    overflow: 'hidden',
  },
  confidenceFill: {
    height: '100%',
  },
  confidenceText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  resultCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  resultTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  resultValue: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  resultDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  resultConfidence: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textTertiary,
  },
  characteristicsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  characteristicItem: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    width: '47%',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  characteristicLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  characteristicValue: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    textAlign: 'center',
  },
  recommendationCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.sm,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  priorityBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  priorityText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.semibold,
    textTransform: 'uppercase',
  },
  recommendationTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  recommendationDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    lineHeight: 20,
  },
  recommendationReasoning: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
    lineHeight: 18,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default AIAnalysisStep;