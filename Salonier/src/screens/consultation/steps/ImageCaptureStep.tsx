import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  ScrollView,
  Image,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Camera, CameraType } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';

import { ConsultationFlow, ConsultationFlowData, CapturedImage, ImageAngle } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

const { width, height } = Dimensions.get('window');

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const REQUIRED_ANGLES: { angle: ImageAngle; title: string; description: string; icon: string }[] = [
  {
    angle: 'front',
    title: 'Vista frontal',
    description: 'Cabello desde el frente, cara visible',
    icon: 'account',
  },
  {
    angle: 'back',
    title: 'Vista posterior',
    description: 'Parte trasera del cabello',
    icon: 'account-outline',
  },
  {
    angle: 'close-up-roots',
    title: 'Raíces (primer plano)',
    description: 'Acercamiento a las raíces para ver crecimiento',
    icon: 'magnify-plus',
  },
];

const OPTIONAL_ANGLES: { angle: ImageAngle; title: string; description: string; icon: string }[] = [
  {
    angle: 'left-side',
    title: 'Lado izquierdo',
    description: 'Perfil izquierdo del cabello',
    icon: 'account-arrow-left',
  },
  {
    angle: 'right-side',
    title: 'Lado derecho',
    description: 'Perfil derecho del cabello',
    icon: 'account-arrow-right',
  },
  {
    angle: 'top',
    title: 'Vista superior',
    description: 'Cabello desde arriba',
    icon: 'arrow-up-circle',
  },
  {
    angle: 'close-up-ends',
    title: 'Puntas (primer plano)',
    description: 'Acercamiento a las puntas para ver condición',
    icon: 'magnify-minus',
  },
];

const ImageCaptureStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const [showCamera, setShowCamera] = useState(false);
  const [currentAngle, setCurrentAngle] = useState<ImageAngle>('front');
  const [capturedImages, setCapturedImages] = useState<CapturedImage[]>([]);
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null);
  const cameraRef = useRef<Camera>(null);

  // Verificar permisos de cámara
  const requestCameraPermission = async () => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setCameraPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          'Permisos necesarios',
          'Necesitamos acceso a la cámara para capturar las imágenes del cabello.',
          [
            { text: 'Cancelar', style: 'cancel' },
            { text: 'Configuración', onPress: () => console.log('Abrir configuración') },
          ]
        );
        return false;
      }
      return true;
    } catch (error) {
      Alert.alert('Error', 'No se pudieron verificar los permisos de cámara');
      return false;
    }
  };

  const openCamera = async (angle: ImageAngle) => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) return;

    setCurrentAngle(angle);
    setShowCamera(true);
  };

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      setIsLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });

      // Simular análisis de calidad
      const qualityScore = Math.random() * 0.4 + 0.6; // 0.6-1.0

      const newImage: CapturedImage = {
        id: `img-${Date.now()}`,
        uri: photo.uri,
        angle: currentAngle,
        timestamp: new Date().toISOString(),
        qualityScore,
        metadata: {
          width: photo.width || 0,
          height: photo.height || 0,
          lighting: qualityScore > 0.8 ? 'excellent' : qualityScore > 0.7 ? 'good' : 'fair',
          focus: qualityScore > 0.8 ? 'excellent' : qualityScore > 0.7 ? 'good' : 'fair',
          angle: qualityScore > 0.8 ? 'excellent' : qualityScore > 0.7 ? 'good' : 'fair',
        },
      };

      setCapturedImages(prev => {
        const filtered = prev.filter(img => img.angle !== currentAngle);
        return [...filtered, newImage];
      });

      setShowCamera(false);

      if (qualityScore < 0.7) {
        Alert.alert(
          'Calidad mejorable',
          'La imagen capturada tiene calidad mejorable. ¿Deseas tomarla de nuevo?',
          [
            { text: 'Mantener', style: 'cancel' },
            { text: 'Repetir', onPress: () => openCamera(currentAngle) },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo capturar la imagen');
    } finally {
      setIsLoading(false);
    }
  };

  const selectFromGallery = async (angle: ImageAngle) => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permisos necesarios',
          'Necesitamos acceso a tu galería para seleccionar imágenes.'
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const qualityScore = 0.75; // Asumimos calidad media para imágenes de galería

        const newImage: CapturedImage = {
          id: `img-${Date.now()}`,
          uri: asset.uri,
          angle,
          timestamp: new Date().toISOString(),
          qualityScore,
          metadata: {
            width: asset.width || 0,
            height: asset.height || 0,
            lighting: 'good',
            focus: 'good',
            angle: 'good',
          },
        };

        setCapturedImages(prev => {
          const filtered = prev.filter(img => img.angle !== angle);
          return [...filtered, newImage];
        });
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo seleccionar la imagen');
    }
  };

  const removeImage = (imageId: string) => {
    setCapturedImages(prev => prev.filter(img => img.id !== imageId));
  };

  const getImageForAngle = (angle: ImageAngle): CapturedImage | undefined => {
    return capturedImages.find(img => img.angle === angle);
  };

  const getRequiredImagesCount = (): { captured: number; total: number } => {
    const capturedRequired = REQUIRED_ANGLES.filter(angle => 
      getImageForAngle(angle.angle)
    ).length;
    
    return {
      captured: capturedRequired,
      total: REQUIRED_ANGLES.length,
    };
  };

  const handleContinue = () => {
    const { captured, total } = getRequiredImagesCount();
    
    if (captured < total) {
      Alert.alert(
        'Imágenes faltantes',
        `Faltan ${total - captured} imágenes obligatorias. Por favor captura todas las vistas requeridas.`
      );
      return;
    }

    // Guardar datos de captura de imágenes
    updateFlowData({
      imageCapture: {
        capturedImages,
        qualityChecks: capturedImages.map(img => ({
          imageId: img.id,
          overallScore: img.qualityScore,
          lightingScore: img.qualityScore,
          focusScore: img.qualityScore,
          angleScore: img.qualityScore,
          recommendations: img.qualityScore < 0.7 ? ['Mejorar iluminación', 'Verificar enfoque'] : [],
          approved: img.qualityScore >= 0.6,
        })),
        captureNotes: `${capturedImages.length} imágenes capturadas`,
      },
    });

    goToNextStep();
  };

  const AngleCard: React.FC<{
    angle: ImageAngle;
    title: string;
    description: string;
    icon: string;
    required: boolean;
  }> = ({ angle, title, description, icon, required }) => {
    const image = getImageForAngle(angle);
    const hasImage = !!image;

    return (
      <View style={[styles.angleCard, hasImage && styles.angleCardCompleted]}>
        <View style={styles.angleHeader}>
          <View style={styles.angleIcon}>
            <MaterialCommunityIcons 
              name={icon as any} 
              size={24} 
              color={hasImage ? COLORS.success : COLORS.gray400} 
            />
          </View>
          <View style={styles.angleInfo}>
            <Text style={styles.angleTitle}>{title}</Text>
            <Text style={styles.angleDescription}>{description}</Text>
            {required && (
              <Text style={styles.requiredLabel}>Obligatorio</Text>
            )}
          </View>
          {hasImage && (
            <View style={styles.completedBadge}>
              <MaterialCommunityIcons 
                name="check-circle" 
                size={20} 
                color={COLORS.success} 
              />
            </View>
          )}
        </View>

        {hasImage ? (
          <View style={styles.imagePreview}>
            <Image source={{ uri: image.uri }} style={styles.previewImage} />
            <View style={styles.imageActions}>
              <TouchableOpacity 
                style={styles.retakeButton}
                onPress={() => openCamera(angle)}
              >
                <MaterialCommunityIcons 
                  name="camera-retake" 
                  size={16} 
                  color={COLORS.textInverse} 
                />
                <Text style={styles.retakeText}>Repetir</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.removeButton}
                onPress={() => removeImage(image.id)}
              >
                <MaterialCommunityIcons 
                  name="delete" 
                  size={16} 
                  color={COLORS.textInverse} 
                />
              </TouchableOpacity>
            </View>
            <View style={styles.qualityIndicator}>
              <Text style={styles.qualityText}>
                Calidad: {Math.round(image.qualityScore * 100)}%
              </Text>
            </View>
          </View>
        ) : (
          <View style={styles.captureActions}>
            <TouchableOpacity 
              style={styles.cameraButton}
              onPress={() => openCamera(angle)}
            >
              <MaterialCommunityIcons 
                name="camera" 
                size={20} 
                color={COLORS.textInverse} 
              />
              <Text style={styles.cameraButtonText}>Cámara</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.galleryButton}
              onPress={() => selectFromGallery(angle)}
            >
              <MaterialCommunityIcons 
                name="image" 
                size={20} 
                color={COLORS.primary} 
              />
              <Text style={styles.galleryButtonText}>Galería</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          type={CameraType.back}
          ratio="4:3"
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity 
                style={styles.closeCamera}
                onPress={() => setShowCamera(false)}
              >
                <MaterialCommunityIcons 
                  name="close" 
                  size={24} 
                  color={COLORS.textInverse} 
                />
              </TouchableOpacity>
              <Text style={styles.cameraTitle}>
                {REQUIRED_ANGLES.find(a => a.angle === currentAngle)?.title ||
                 OPTIONAL_ANGLES.find(a => a.angle === currentAngle)?.title}
              </Text>
            </View>

            <View style={styles.cameraGuide}>
              <View style={styles.guideFrame} />
              <Text style={styles.guideText}>
                Centra el cabello en el marco
              </Text>
            </View>

            <View style={styles.cameraControls}>
              <TouchableOpacity 
                style={styles.captureButton}
                onPress={takePicture}
                disabled={isLoading}
              >
                <View style={styles.captureButtonInner} />
              </TouchableOpacity>
            </View>
          </View>
        </Camera>
      </View>
    );
  }

  const { captured, total } = getRequiredImagesCount();

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Captura de Imágenes</Text>
          <Text style={styles.subtitle}>
            Captura imágenes del cabello desde diferentes ángulos para un análisis preciso
          </Text>
        </View>

        {/* Progreso */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>
            Progreso: {captured}/{total} imágenes obligatorias
          </Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(captured / total) * 100}%` }
              ]} 
            />
          </View>
        </View>

        {/* Imágenes obligatorias */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Imágenes Obligatorias</Text>
          {REQUIRED_ANGLES.map((angleData) => (
            <AngleCard
              key={angleData.angle}
              angle={angleData.angle}
              title={angleData.title}
              description={angleData.description}
              icon={angleData.icon}
              required={true}
            />
          ))}
        </View>

        {/* Imágenes opcionales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Imágenes Opcionales</Text>
          <Text style={styles.sectionSubtitle}>
            Estas imágenes mejorarán la precisión del análisis
          </Text>
          {OPTIONAL_ANGLES.map((angleData) => (
            <AngleCard
              key={angleData.angle}
              angle={angleData.angle}
              title={angleData.title}
              description={angleData.description}
              icon={angleData.icon}
              required={false}
            />
          ))}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={20} 
            color={COLORS.textSecondary} 
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[
            styles.continueButton,
            captured < total && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={captured < total}
        >
          <Text style={styles.continueButtonText}>
            {captured < total ? `Faltan ${total - captured} imágenes` : 'Analizar con IA'}
          </Text>
          <MaterialCommunityIcons 
            name="brain" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    paddingVertical: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
  },
  progressSection: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.lg,
    ...SHADOWS.sm,
  },
  progressTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  progressBar: {
    height: 8,
    backgroundColor: COLORS.gray200,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.success,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  sectionSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
  },
  angleCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 2,
    borderColor: 'transparent',
    ...SHADOWS.sm,
  },
  angleCardCompleted: {
    borderColor: COLORS.success + '30',
    backgroundColor: COLORS.success + '05',
  },
  angleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  angleIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  angleInfo: {
    flex: 1,
  },
  angleTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  angleDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  requiredLabel: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.warning,
    fontWeight: TYPOGRAPHY.weights.medium,
    marginTop: 2,
  },
  completedBadge: {
    marginLeft: SPACING.sm,
  },
  imagePreview: {
    position: 'relative',
  },
  previewImage: {
    width: '100%',
    height: 120,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray100,
  },
  imageActions: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    flexDirection: 'row',
    gap: SPACING.xs,
  },
  retakeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary + 'CC',
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    gap: 2,
  },
  retakeText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  removeButton: {
    backgroundColor: COLORS.error + 'CC',
    borderRadius: BORDER_RADIUS.sm,
    padding: SPACING.xs,
  },
  qualityIndicator: {
    position: 'absolute',
    bottom: SPACING.sm,
    left: SPACING.sm,
    backgroundColor: COLORS.background + 'CC',
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
  },
  qualityText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textPrimary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  captureActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  cameraButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    gap: SPACING.xs,
  },
  cameraButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  galleryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primary,
    gap: SPACING.xs,
  },
  galleryButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  cameraHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
  },
  closeCamera: {
    padding: SPACING.sm,
  },
  cameraTitle: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginRight: 40, // Para centrar considerando el botón de cerrar
  },
  cameraGuide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guideFrame: {
    width: width * 0.7,
    height: width * 0.7,
    borderWidth: 2,
    borderColor: COLORS.textInverse,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'transparent',
  },
  guideText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginTop: SPACING.lg,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  cameraControls: {
    alignItems: 'center',
    paddingBottom: 50,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.textInverse,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.primary,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonDisabled: {
    backgroundColor: COLORS.gray300,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
    textAlign: 'center',
  },
});

export default ImageCaptureStep;
