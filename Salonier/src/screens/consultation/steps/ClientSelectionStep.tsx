import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { mockClients } from '../../../data/mock/clients';
import { Client, ConsultationFlow, ConsultationFlowData } from '../../../types';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const ClientSelectionStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  isLoading,
  setIsLoading,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showGuestForm, setShowGuestForm] = useState(false);
  const [guestClientData, setGuestClientData] = useState({
    name: '',
    phone: '',
    email: '',
  });

  // Filtrar clientes por búsqueda
  const filteredClients = mockClients.filter(client =>
    client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    client.phone?.includes(searchQuery)
  );

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setShowGuestForm(false);
    updateFlowData({
      clientSelection: {
        selectedClientId: client.id,
        isGuestClient: false,
      },
    });
  };

  const handleGuestClient = () => {
    setSelectedClient(null);
    setShowGuestForm(true);
  };

  const handleGuestDataChange = (field: keyof typeof guestClientData, value: string) => {
    setGuestClientData(prev => ({ ...prev, [field]: value }));
  };

  const validateGuestData = (): boolean => {
    if (!guestClientData.name.trim()) {
      Alert.alert('Error', 'El nombre es obligatorio para clientes invitados');
      return false;
    }
    return true;
  };

  const handleContinue = () => {
    if (selectedClient) {
      // Cliente existente seleccionado
      goToNextStep();
    } else if (showGuestForm) {
      // Cliente invitado
      if (!validateGuestData()) return;
      
      updateFlowData({
        clientSelection: {
          selectedClientId: 'guest',
          isGuestClient: true,
          guestClientInfo: guestClientData,
        },
      });
      goToNextStep();
    } else {
      Alert.alert('Selección requerida', 'Por favor selecciona un cliente o crea uno invitado');
    }
  };

  const ClientCard: React.FC<{ client: Client; isSelected: boolean }> = ({ client, isSelected }) => {
    const hasAllergies = client.allergies.length > 0;
    
    return (
      <TouchableOpacity 
        style={[styles.clientCard, isSelected && styles.clientCardSelected]}
        onPress={() => handleClientSelect(client)}
      >
        <View style={styles.clientHeader}>
          <View style={styles.clientImageContainer}>
            {client.profileImage ? (
              <Image source={{ uri: client.profileImage }} style={styles.clientImage} />
            ) : (
              <View style={styles.clientImagePlaceholder}>
                <MaterialCommunityIcons 
                  name="account" 
                  size={24} 
                  color={COLORS.gray400} 
                />
              </View>
            )}
            {hasAllergies && (
              <View style={styles.allergyBadge}>
                <MaterialCommunityIcons 
                  name="alert-circle" 
                  size={12} 
                  color={COLORS.textInverse} 
                />
              </View>
            )}
          </View>
          
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{client.name}</Text>
            {client.phone && (
              <Text style={styles.clientDetail}>{client.phone}</Text>
            )}
            {client.email && (
              <Text style={styles.clientDetail}>{client.email}</Text>
            )}
          </View>

          <View style={styles.clientMeta}>
            <Text style={styles.visitCount}>
              {client.history.length} visitas
            </Text>
            {hasAllergies && (
              <View style={styles.allergyWarning}>
                <MaterialCommunityIcons 
                  name="alert-circle-outline" 
                  size={14} 
                  color={COLORS.warning} 
                />
                <Text style={styles.allergyText}>Alergias</Text>
              </View>
            )}
          </View>
        </View>

        {isSelected && (
          <View style={styles.selectedIndicator}>
            <MaterialCommunityIcons 
              name="check-circle" 
              size={20} 
              color={COLORS.success} 
            />
            <Text style={styles.selectedText}>Cliente seleccionado</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const GuestClientForm: React.FC = () => (
    <View style={styles.guestForm}>
      <View style={styles.guestHeader}>
        <MaterialCommunityIcons 
          name="account-plus" 
          size={32} 
          color={COLORS.secondary} 
        />
        <Text style={styles.guestTitle}>Cliente Invitado</Text>
        <Text style={styles.guestSubtitle}>
          Para consultas rápidas sin crear perfil permanente
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Nombre *</Text>
        <TextInput
          style={styles.input}
          value={guestClientData.name}
          onChangeText={(value) => handleGuestDataChange('name', value)}
          placeholder="Nombre del cliente"
          autoCapitalize="words"
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Teléfono</Text>
        <TextInput
          style={styles.input}
          value={guestClientData.phone}
          onChangeText={(value) => handleGuestDataChange('phone', value)}
          placeholder="+34 666 123 456"
          keyboardType="phone-pad"
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Email</Text>
        <TextInput
          style={styles.input}
          value={guestClientData.email}
          onChangeText={(value) => handleGuestDataChange('email', value)}
          placeholder="<EMAIL>"
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      <View style={styles.guestNote}>
        <MaterialCommunityIcons 
          name="information-outline" 
          size={16} 
          color={COLORS.info} 
        />
        <Text style={styles.guestNoteText}>
          Los datos del cliente invitado se guardarán solo para esta consulta
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Seleccionar Cliente</Text>
          <Text style={styles.subtitle}>
            Elige un cliente existente o crea uno invitado para la consulta
          </Text>
        </View>

        {!showGuestForm ? (
          <>
            {/* Búsqueda */}
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <MaterialCommunityIcons 
                  name="magnify" 
                  size={20} 
                  color={COLORS.gray400}
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Buscar cliente..."
                  placeholderTextColor={COLORS.gray400}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCorrect={false}
                />
              </View>
              
              <TouchableOpacity 
                style={styles.guestButton}
                onPress={handleGuestClient}
              >
                <MaterialCommunityIcons 
                  name="account-plus" 
                  size={20} 
                  color={COLORS.textInverse} 
                />
                <Text style={styles.guestButtonText}>Invitado</Text>
              </TouchableOpacity>
            </View>

            {/* Lista de clientes */}
            <FlatList
              data={filteredClients}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ClientCard 
                  client={item} 
                  isSelected={selectedClient?.id === item.id}
                />
              )}
              style={styles.clientList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyState}>
                  <MaterialCommunityIcons 
                    name="account-search" 
                    size={48} 
                    color={COLORS.gray300} 
                  />
                  <Text style={styles.emptyTitle}>No se encontraron clientes</Text>
                  <Text style={styles.emptyText}>
                    Intenta con otros términos o crea un cliente invitado
                  </Text>
                </View>
              }
            />
          </>
        ) : (
          <GuestClientForm />
        )}
      </View>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        {showGuestForm && (
          <TouchableOpacity 
            style={styles.backToListButton}
            onPress={() => setShowGuestForm(false)}
          >
            <MaterialCommunityIcons 
              name="arrow-left" 
              size={20} 
              color={COLORS.textSecondary} 
            />
            <Text style={styles.backToListText}>Volver a lista</Text>
          </TouchableOpacity>
        )}

        <View style={styles.buttonSpacer} />

        <TouchableOpacity 
          style={[
            styles.continueButton,
            (!selectedClient && !showGuestForm) && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={!selectedClient && !showGuestForm}
        >
          <Text style={styles.continueButtonText}>Continuar</Text>
          <MaterialCommunityIcons 
            name="arrow-right" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  header: {
    paddingVertical: SPACING.lg,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    lineHeight: 22,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: SPACING.lg,
    gap: SPACING.sm,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
  },
  guestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    gap: SPACING.xs,
  },
  guestButtonText: {
    color: COLORS.textInverse,
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  clientList: {
    flex: 1,
  },
  clientCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 2,
    borderColor: 'transparent',
    ...SHADOWS.sm,
  },
  clientCardSelected: {
    borderColor: COLORS.success,
    backgroundColor: COLORS.success + '05',
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientImageContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  clientImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  clientImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  allergyBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.warning,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  clientDetail: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  clientMeta: {
    alignItems: 'flex-end',
  },
  visitCount: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  allergyWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  allergyText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.warning,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  selectedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.sm,
    paddingTop: SPACING.sm,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray100,
    gap: SPACING.xs,
  },
  selectedText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.success,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  guestForm: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginTop: SPACING.md,
  },
  guestHeader: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  guestTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  guestSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  inputLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  input: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  guestNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.info + '10',
    borderRadius: BORDER_RADIUS.sm,
    padding: SPACING.sm,
    marginTop: SPACING.md,
    gap: SPACING.xs,
  },
  guestNoteText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.info,
    lineHeight: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  emptyText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backToListButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backToListText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  buttonSpacer: {
    flex: 1,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  continueButtonDisabled: {
    opacity: 0.5,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
});

export default ClientSelectionStep;
