import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { ConsultationFlow, ConsultationFlowData, StylistValidation, ZoneValidation, ManualAdjustment } from '../../../types';
import { useAuth } from '../../../contexts/AuthContext';
import ZoneAnalysisDisplay from '../../../components/consultation/ZoneAnalysisDisplay';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../../constants';

interface Props {
  consultationFlow: ConsultationFlow;
  updateFlowData: (data: Partial<ConsultationFlowData>) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AIValidationStep: React.FC<Props> = ({
  consultationFlow,
  updateFlowData,
  goToNextStep,
  goToPreviousStep,
  isLoading,
  setIsLoading,
}) => {
  const { user } = useAuth();
  const [selectedZone, setSelectedZone] = useState<'roots' | 'mids' | 'ends'>('roots');
  const [overallApproval, setOverallApproval] = useState<boolean | null>(null);
  const [confidenceRating, setConfidenceRating] = useState(4);
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [zoneValidations, setZoneValidations] = useState<ZoneValidation[]>([]);
  const [manualAdjustments, setManualAdjustments] = useState<ManualAdjustment[]>([]);

  const aiAnalysis = consultationFlow.data.aiAnalysis;
  const capturedImages = consultationFlow.data.imageCapture?.capturedImages || [];

  if (!aiAnalysis) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={COLORS.error} />
        <Text style={styles.errorText}>No hay análisis de IA para validar</Text>
      </View>
    );
  }

  const handleZoneApproval = (zone: 'roots' | 'mids' | 'ends', approved: boolean) => {
    setZoneValidations(prev => {
      const existing = prev.find(v => v.zone === zone);
      if (existing) {
        return prev.map(v => v.zone === zone ? { ...v, approved } : v);
      } else {
        return [...prev, {
          zone,
          approved,
          corrections: [],
          additionalObservations: '',
        }];
      }
    });
  };

  const addCorrection = (zone: 'roots' | 'mids' | 'ends', parameter: string, aiValue: any, correctedValue: any, reason: string) => {
    setZoneValidations(prev => {
      return prev.map(v => {
        if (v.zone === zone) {
          return {
            ...v,
            corrections: [...v.corrections, { parameter, aiValue, correctedValue, reason }],
          };
        }
        return v;
      });
    });

    // También agregar a ajustes manuales
    setManualAdjustments(prev => [...prev, {
      parameter: `${zone}_${parameter}`,
      originalValue: aiValue,
      adjustedValue: correctedValue,
      reason,
      confidence: 'high',
    }]);
  };

  const getZoneValidation = (zone: 'roots' | 'mids' | 'ends'): ZoneValidation | undefined => {
    return zoneValidations.find(v => v.zone === zone);
  };

  const handleContinue = () => {
    if (overallApproval === null) {
      Alert.alert('Validación requerida', 'Por favor indica si apruebas el análisis general de IA');
      return;
    }

    // Crear validación del estilista
    const stylistValidation: StylistValidation = {
      validatedAt: new Date().toISOString(),
      stylistId: user?.id || '',
      overallApproval,
      zoneValidations,
      manualAdjustments,
      additionalNotes,
      confidenceRating,
    };

    // Actualizar datos del análisis con la validación
    updateFlowData({
      aiAnalysis: {
        ...aiAnalysis,
        stylistValidation,
      },
    });

    goToNextStep();
  };

  const ConfidenceRating: React.FC = () => (
    <View style={styles.confidenceSection}>
      <Text style={styles.confidenceTitle}>
        ¿Qué tan confiable consideras este análisis de IA?
      </Text>
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <TouchableOpacity
            key={rating}
            style={[
              styles.ratingButton,
              confidenceRating >= rating && styles.ratingButtonActive,
            ]}
            onPress={() => setConfidenceRating(rating)}
          >
            <MaterialCommunityIcons 
              name="star" 
              size={24} 
              color={confidenceRating >= rating ? COLORS.warning : COLORS.gray300}
            />
          </TouchableOpacity>
        ))}
      </View>
      <Text style={styles.ratingDescription}>
        {confidenceRating === 1 ? 'Muy poco confiable' :
         confidenceRating === 2 ? 'Poco confiable' :
         confidenceRating === 3 ? 'Moderadamente confiable' :
         confidenceRating === 4 ? 'Confiable' : 'Muy confiable'}
      </Text>
    </View>
  );

  const ZoneValidationControls: React.FC = () => {
    const zoneData = aiAnalysis.results.zoneAnalysis.find(z => z.zone === selectedZone);
    const validation = getZoneValidation(selectedZone);

    if (!zoneData) return null;

    return (
      <View style={styles.validationControls}>
        <Text style={styles.validationTitle}>
          Validación de {selectedZone === 'roots' ? 'Raíces' : selectedZone === 'mids' ? 'Medios' : 'Puntas'}
        </Text>
        
        <View style={styles.approvalButtons}>
          <TouchableOpacity
            style={[
              styles.approvalButton,
              validation?.approved === true && styles.approvalButtonApproved,
            ]}
            onPress={() => handleZoneApproval(selectedZone, true)}
          >
            <MaterialCommunityIcons 
              name="check-circle" 
              size={20} 
              color={validation?.approved === true ? COLORS.textInverse : COLORS.success}
            />
            <Text style={[
              styles.approvalButtonText,
              validation?.approved === true && styles.approvalButtonTextActive,
            ]}>
              Correcto
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.approvalButton,
              validation?.approved === false && styles.approvalButtonRejected,
            ]}
            onPress={() => handleZoneApproval(selectedZone, false)}
          >
            <MaterialCommunityIcons 
              name="close-circle" 
              size={20} 
              color={validation?.approved === false ? COLORS.textInverse : COLORS.error}
            />
            <Text style={[
              styles.approvalButtonText,
              validation?.approved === false && styles.approvalButtonTextActive,
            ]}>
              Necesita corrección
            </Text>
          </TouchableOpacity>
        </View>

        {validation?.approved === false && (
          <View style={styles.correctionSection}>
            <Text style={styles.correctionTitle}>
              ¿Qué parámetros necesitan corrección?
            </Text>
            <View style={styles.correctionButtons}>
              <TouchableOpacity style={styles.correctionButton}>
                <Text style={styles.correctionButtonText}>Nivel Natural</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.correctionButton}>
                <Text style={styles.correctionButtonText}>Subtono</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.correctionButton}>
                <Text style={styles.correctionButtonText}>Porosidad</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.correctionButton}>
                <Text style={styles.correctionButtonText}>Condición</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerIcon}>
            <MaterialCommunityIcons name="account-check" size={32} color={COLORS.primary} />
          </View>
          <Text style={styles.title}>Validación Profesional</Text>
          <Text style={styles.subtitle}>
            Revisa y confirma los resultados del análisis de IA
          </Text>
        </View>

        {/* Aprobación general */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Aprobación General</Text>
          <Text style={styles.sectionDescription}>
            ¿El análisis general de IA es correcto y útil para la consulta?
          </Text>
          
          <View style={styles.overallApprovalButtons}>
            <TouchableOpacity
              style={[
                styles.overallButton,
                overallApproval === true && styles.overallButtonApproved,
              ]}
              onPress={() => setOverallApproval(true)}
            >
              <MaterialCommunityIcons 
                name="thumb-up" 
                size={24} 
                color={overallApproval === true ? COLORS.textInverse : COLORS.success}
              />
              <Text style={[
                styles.overallButtonText,
                overallApproval === true && styles.overallButtonTextActive,
              ]}>
                Apruebo el análisis
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.overallButton,
                overallApproval === false && styles.overallButtonRejected,
              ]}
              onPress={() => setOverallApproval(false)}
            >
              <MaterialCommunityIcons 
                name="thumb-down" 
                size={24} 
                color={overallApproval === false ? COLORS.textInverse : COLORS.error}
              />
              <Text style={[
                styles.overallButtonText,
                overallApproval === false && styles.overallButtonTextActive,
              ]}>
                Requiere correcciones
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Análisis por zonas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Validación por Zonas</Text>
          <ZoneAnalysisDisplay
            zoneAnalysis={aiAnalysis.results.zoneAnalysis}
            capturedImages={capturedImages}
            onZoneSelect={setSelectedZone}
            selectedZone={selectedZone}
          />
          <ZoneValidationControls />
        </View>

        {/* Rating de confianza */}
        <View style={styles.section}>
          <ConfidenceRating />
        </View>

        {/* Notas adicionales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notas Adicionales</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Agrega observaciones adicionales sobre el análisis..."
            placeholderTextColor={COLORS.gray400}
            value={additionalNotes}
            onChangeText={setAdditionalNotes}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={goToPreviousStep}
        >
          <MaterialCommunityIcons 
            name="arrow-left" 
            size={20} 
            color={COLORS.textSecondary} 
          />
          <Text style={styles.backButtonText}>Anterior</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[
            styles.continueButton,
            overallApproval === null && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={overallApproval === null}
        >
          <Text style={styles.continueButtonText}>
            {overallApproval ? 'Continuar con Color Objetivo' : 'Continuar con Correcciones'}
          </Text>
          <MaterialCommunityIcons 
            name="arrow-right" 
            size={20} 
            color={COLORS.textInverse} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  errorText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.error,
    textAlign: 'center',
    marginTop: SPACING.md,
  },
  header: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.lg,
  },
  headerIcon: {
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  section: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  sectionDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  overallApprovalButtons: {
    gap: SPACING.md,
  },
  overallButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    borderWidth: 2,
    borderColor: 'transparent',
    gap: SPACING.sm,
  },
  overallButtonApproved: {
    backgroundColor: COLORS.success,
    borderColor: COLORS.success,
  },
  overallButtonRejected: {
    backgroundColor: COLORS.error,
    borderColor: COLORS.error,
  },
  overallButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  overallButtonTextActive: {
    color: COLORS.textInverse,
  },
  validationControls: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginTop: SPACING.md,
  },
  validationTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  approvalButtons: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  approvalButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    borderWidth: 2,
    borderColor: 'transparent',
    gap: SPACING.xs,
  },
  approvalButtonApproved: {
    backgroundColor: COLORS.success,
    borderColor: COLORS.success,
  },
  approvalButtonRejected: {
    backgroundColor: COLORS.error,
    borderColor: COLORS.error,
  },
  approvalButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  approvalButtonTextActive: {
    color: COLORS.textInverse,
  },
  correctionSection: {
    marginTop: SPACING.md,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  correctionTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
  },
  correctionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  correctionButton: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  correctionButtonText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
  },
  confidenceSection: {
    alignItems: 'center',
  },
  confidenceTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  ratingButton: {
    padding: SPACING.xs,
  },
  ratingButtonActive: {
    transform: [{ scale: 1.1 }],
  },
  ratingDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  notesInput: {
    backgroundColor: COLORS.gray50,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    minHeight: 100,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  continueButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginLeft: SPACING.md,
    gap: SPACING.xs,
  },
  continueButtonDisabled: {
    backgroundColor: COLORS.gray300,
  },
  continueButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
    textAlign: 'center',
  },
});

export default AIValidationStep;
