import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants';

const { width, height } = Dimensions.get('window');

type OnboardingScreenNavigationProp = StackNavigationProp<any, 'Onboarding'>;

interface Props {
  navigation: OnboardingScreenNavigationProp;
  onComplete: () => void;
}

interface OnboardingSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  gradient: string[];
}

const OnboardingScreen: React.FC<Props> = ({ navigation, onComplete }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const slides: OnboardingSlide[] = [
    {
      id: '1',
      title: '¡Bienvenido a Salonier!',
      subtitle: 'Tu asistente inteligente de coloración',
      description: 'Revoluciona tu trabajo con IA especializada en coloración capilar. Diagnósticos precisos, fórmulas perfectas y gestión profesional.',
      icon: 'palette',
      gradient: [COLORS.primary, COLORS.primaryDark],
    },
    {
      id: '2',
      title: 'Diagnóstico con IA',
      subtitle: 'Análisis profesional instantáneo',
      description: 'Captura fotos del cabello y obtén análisis detallados: nivel natural, subtono, porosidad, canas y más. Como tener un experto siempre contigo.',
      icon: 'camera-iris',
      gradient: [COLORS.secondary, COLORS.secondaryDark],
    },
    {
      id: '3',
      title: 'Fórmulas Inteligentes',
      subtitle: 'Conversión automática entre marcas',
      description: 'Genera fórmulas precisas adaptadas a tus productos favoritos. Convierte automáticamente entre diferentes marcas y líneas.',
      icon: 'flask',
      gradient: [COLORS.accent, COLORS.accentDark],
    },
    {
      id: '4',
      title: 'Gestión Completa',
      subtitle: 'Todo tu salón en una app',
      description: 'Clientes, citas, inventario y analíticas. Optimiza tu rentabilidad y ofrece un servicio excepcional a tus clientes.',
      icon: 'chart-line',
      gradient: [COLORS.info, '#2563EB'],
    },
    {
      id: '5',
      title: '¡Comencemos!',
      subtitle: 'Tu transformación digital empieza ahora',
      description: 'Configura tu perfil, selecciona tus marcas favoritas y comienza a crear consultas profesionales con IA.',
      icon: 'rocket-launch',
      gradient: [COLORS.success, '#059669'],
    },
  ];

  const goToNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      onComplete();
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
    }
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    flatListRef.current?.scrollToIndex({ index, animated: true });
  };

  const renderSlide = ({ item }: { item: OnboardingSlide }) => (
    <View style={styles.slide}>
      <LinearGradient colors={item.gradient} style={styles.slideGradient}>
        <View style={styles.slideContent}>
          {/* Icono */}
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons 
              name={item.icon as any} 
              size={80} 
              color={COLORS.textInverse} 
            />
          </View>

          {/* Contenido */}
          <View style={styles.textContainer}>
            <Text style={styles.slideTitle}>{item.title}</Text>
            <Text style={styles.slideSubtitle}>{item.subtitle}</Text>
            <Text style={styles.slideDescription}>{item.description}</Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {slides.map((_, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.paginationDot,
            index === currentIndex && styles.paginationDotActive
          ]}
          onPress={() => goToSlide(index)}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Slides */}
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderSlide}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / width);
          setCurrentIndex(index);
        }}
        scrollEnabled={false} // Controlamos la navegación con botones
      />

      {/* Controles */}
      <View style={styles.controls}>
        {/* Paginación */}
        {renderPagination()}

        {/* Botones */}
        <View style={styles.buttonContainer}>
          {currentIndex > 0 && (
            <TouchableOpacity style={styles.backButton} onPress={goToPrevious}>
              <MaterialCommunityIcons 
                name="chevron-left" 
                size={24} 
                color={COLORS.textSecondary} 
              />
              <Text style={styles.backButtonText}>Anterior</Text>
            </TouchableOpacity>
          )}

          <View style={styles.buttonSpacer} />

          <TouchableOpacity style={styles.nextButton} onPress={goToNext}>
            <Text style={styles.nextButtonText}>
              {currentIndex === slides.length - 1 ? 'Comenzar' : 'Siguiente'}
            </Text>
            <MaterialCommunityIcons 
              name={currentIndex === slides.length - 1 ? 'check' : 'chevron-right'} 
              size={24} 
              color={COLORS.textInverse} 
            />
          </TouchableOpacity>
        </View>

        {/* Botón saltar */}
        {currentIndex < slides.length - 1 && (
          <TouchableOpacity style={styles.skipButton} onPress={onComplete}>
            <Text style={styles.skipButtonText}>Saltar introducción</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  slide: {
    width,
    height,
  },
  slideGradient: {
    flex: 1,
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.xxl,
  },
  textContainer: {
    alignItems: 'center',
    maxWidth: '90%',
  },
  slideTitle: {
    fontSize: TYPOGRAPHY.sizes.xxxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  slideSubtitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    opacity: 0.9,
  },
  slideDescription: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    textAlign: 'center',
    lineHeight: 24,
    opacity: 0.8,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
    gap: SPACING.sm,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.gray300,
  },
  paginationDotActive: {
    backgroundColor: COLORS.primary,
    width: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  backButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  buttonSpacer: {
    flex: 1,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  nextButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  skipButton: {
    alignSelf: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
  },
  skipButtonText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textTertiary,
    textAlign: 'center',
  },
});

export default OnboardingScreen;
