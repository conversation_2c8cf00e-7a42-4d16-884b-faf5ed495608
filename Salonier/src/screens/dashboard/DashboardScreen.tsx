import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';

import { useAuth } from '../../contexts/AuthContext';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

const { width } = Dimensions.get('window');

const DashboardScreen: React.FC = () => {
  const { user } = useAuth();
  const navigation = useNavigation();

  // Datos mock para el dashboard
  const todayStats = {
    appointments: 8,
    completedConsultations: 5,
    revenue: 420,
    newClients: 2,
  };

  const quickActions = [
    {
      id: 'new-consultation',
      title: 'Nueva Consulta',
      subtitle: 'Iniciar diagnóstico IA',
      icon: 'palette',
      color: COLORS.primary,
      onPress: () => navigation.navigate('ConsultationFlow' as never),
    },
    {
      id: 'schedule-appointment',
      title: 'Agendar Cita',
      subtitle: 'Calendario disponible',
      icon: 'calendar-plus',
      color: COLORS.secondary,
      onPress: () => console.log('Agendar cita'),
    },
    {
      id: 'add-client',
      title: 'Nuevo Cliente',
      subtitle: 'Crear perfil',
      icon: 'account-plus',
      color: COLORS.accent,
      onPress: () => console.log('Nuevo cliente'),
    },
    {
      id: 'inventory-check',
      title: 'Inventario',
      subtitle: 'Revisar stock',
      icon: 'package-variant',
      color: COLORS.info,
      onPress: () => console.log('Inventario'),
    },
  ];

  const recentActivity = [
    {
      id: '1',
      type: 'consultation',
      client: 'Elena Rodríguez',
      action: 'Consulta completada',
      time: 'Hace 2 horas',
      icon: 'check-circle',
      color: COLORS.success,
    },
    {
      id: '2',
      type: 'appointment',
      client: 'Carmen Jiménez',
      action: 'Cita confirmada para mañana',
      time: 'Hace 3 horas',
      icon: 'calendar-check',
      color: COLORS.info,
    },
    {
      id: '3',
      type: 'inventory',
      client: 'Sistema',
      action: 'Stock bajo: Wella Koleston 7/3',
      time: 'Hace 5 horas',
      icon: 'alert-circle',
      color: COLORS.warning,
    },
  ];

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle: string;
    icon: string;
    color: string;
  }> = ({ title, value, subtitle, icon, color }) => (
    <View style={styles.statCard}>
      <View style={styles.statHeader}>
        <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
          <MaterialCommunityIcons name={icon as any} size={24} color={color} />
        </View>
        <Text style={styles.statValue}>{value}</Text>
      </View>
      <Text style={styles.statTitle}>{title}</Text>
      <Text style={styles.statSubtitle}>{subtitle}</Text>
    </View>
  );

  const QuickActionCard: React.FC<{
    title: string;
    subtitle: string;
    icon: string;
    color: string;
    onPress: () => void;
  }> = ({ title, subtitle, icon, color, onPress }) => (
    <TouchableOpacity style={styles.quickActionCard} onPress={onPress}>
      <LinearGradient
        colors={[color, color + 'CC']}
        style={styles.quickActionGradient}
      >
        <MaterialCommunityIcons name={icon as any} size={32} color={COLORS.textInverse} />
        <Text style={styles.quickActionTitle}>{title}</Text>
        <Text style={styles.quickActionSubtitle}>{subtitle}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header de bienvenida */}
      <View style={styles.header}>
        <View>
          <Text style={styles.welcomeText}>¡Hola, {user?.name?.split(' ')[0]}!</Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('es-ES', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <MaterialCommunityIcons name="bell-outline" size={24} color={COLORS.textPrimary} />
          <View style={styles.notificationBadge}>
            <Text style={styles.notificationBadgeText}>3</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Estadísticas del día */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Resumen de Hoy</Text>
        <View style={styles.statsContainer}>
          <StatCard
            title="Citas"
            value={todayStats.appointments}
            subtitle="programadas"
            icon="calendar-clock"
            color={COLORS.primary}
          />
          <StatCard
            title="Consultas"
            value={todayStats.completedConsultations}
            subtitle="completadas"
            icon="palette"
            color={COLORS.secondary}
          />
          <StatCard
            title="Ingresos"
            value={`€${todayStats.revenue}`}
            subtitle="generados"
            icon="currency-eur"
            color={COLORS.success}
          />
          <StatCard
            title="Clientes"
            value={todayStats.newClients}
            subtitle="nuevos"
            icon="account-plus"
            color={COLORS.accent}
          />
        </View>
      </View>

      {/* Acciones rápidas */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
        <View style={styles.quickActionsContainer}>
          {quickActions.map((action) => (
            <QuickActionCard
              key={action.id}
              title={action.title}
              subtitle={action.subtitle}
              icon={action.icon}
              color={action.color}
              onPress={action.onPress}
            />
          ))}
        </View>
      </View>

      {/* Actividad reciente */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actividad Reciente</Text>
        <View style={styles.activityContainer}>
          {recentActivity.map((activity) => (
            <View key={activity.id} style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: activity.color + '20' }]}>
                <MaterialCommunityIcons 
                  name={activity.icon as any} 
                  size={20} 
                  color={activity.color} 
                />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityAction}>{activity.action}</Text>
                <Text style={styles.activityClient}>{activity.client}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Espaciado inferior */}
      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    backgroundColor: COLORS.background,
  },
  welcomeText: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  dateText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textTransform: 'capitalize',
  },
  notificationButton: {
    position: 'relative',
    padding: SPACING.sm,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: COLORS.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: COLORS.textInverse,
    fontSize: 12,
    fontWeight: TYPOGRAPHY.weights.bold,
  },
  section: {
    marginTop: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  statCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    width: (width - SPACING.lg * 2 - SPACING.md) / 2,
    ...SHADOWS.sm,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  statTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  quickActionCard: {
    width: (width - SPACING.lg * 2 - SPACING.md) / 2,
    height: 120,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.md,
  },
  quickActionGradient: {
    flex: 1,
    padding: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textInverse,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  quickActionSubtitle: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textInverse,
    textAlign: 'center',
    opacity: 0.9,
    marginTop: 2,
  },
  activityContainer: {
    backgroundColor: COLORS.background,
    marginHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    ...SHADOWS.sm,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray100,
  },
  activityIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  activityContent: {
    flex: 1,
  },
  activityAction: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  activityClient: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  activityTime: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textTertiary,
    marginTop: 2,
  },
  bottomSpacing: {
    height: SPACING.xxl,
  },
});

export default DashboardScreen;
