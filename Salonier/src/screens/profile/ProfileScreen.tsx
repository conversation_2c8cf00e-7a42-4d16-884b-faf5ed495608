import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { useAuth } from '../../contexts/AuthContext';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

const ProfileScreen: React.FC = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Cerrar Sesión', 
          style: 'destructive',
          onPress: logout 
        },
      ]
    );
  };

  const menuItems = [
    {
      id: 'edit-profile',
      title: 'Editar Perfil',
      subtitle: 'Actualizar información personal',
      icon: 'account-edit',
      onPress: () => console.log('Editar perfil'),
    },
    {
      id: 'preferences',
      title: 'Preferencias',
      subtitle: 'Marcas favoritas, notificaciones',
      icon: 'cog',
      onPress: () => console.log('Preferencias'),
    },
    {
      id: 'security',
      title: 'Seguridad',
      subtitle: 'Cambiar contraseña, 2FA',
      icon: 'shield-account',
      onPress: () => console.log('Seguridad'),
    },
    {
      id: 'help',
      title: 'Ayuda y Soporte',
      subtitle: 'FAQs, contactar soporte',
      icon: 'help-circle',
      onPress: () => console.log('Ayuda'),
    },
    {
      id: 'about',
      title: 'Acerca de Salonier',
      subtitle: 'Versión, términos, privacidad',
      icon: 'information',
      onPress: () => console.log('Acerca de'),
    },
  ];

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin': return 'Administrador';
      case 'stylist': return 'Estilista';
      case 'receptionist': return 'Recepcionista';
      default: return role;
    }
  };

  const MenuItem: React.FC<{
    title: string;
    subtitle: string;
    icon: string;
    onPress: () => void;
    showArrow?: boolean;
  }> = ({ title, subtitle, icon, onPress, showArrow = true }) => (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <View style={styles.menuIcon}>
        <MaterialCommunityIcons name={icon as any} size={24} color={COLORS.primary} />
      </View>
      <View style={styles.menuContent}>
        <Text style={styles.menuTitle}>{title}</Text>
        <Text style={styles.menuSubtitle}>{subtitle}</Text>
      </View>
      {showArrow && (
        <MaterialCommunityIcons 
          name="chevron-right" 
          size={20} 
          color={COLORS.textTertiary} 
        />
      )}
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header del perfil */}
      <View style={styles.profileHeader}>
        <View style={styles.profileImageContainer}>
          {user?.profileImage ? (
            <Image source={{ uri: user.profileImage }} style={styles.profileImage} />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <MaterialCommunityIcons 
                name="account" 
                size={48} 
                color={COLORS.gray400} 
              />
            </View>
          )}
          <TouchableOpacity style={styles.editImageButton}>
            <MaterialCommunityIcons 
              name="camera" 
              size={16} 
              color={COLORS.textInverse} 
            />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.userName}>{user?.name}</Text>
        <Text style={styles.userRole}>{getRoleDisplayName(user?.role || '')}</Text>
        
        <View style={styles.userDetails}>
          {user?.email && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons 
                name="email-outline" 
                size={16} 
                color={COLORS.textSecondary} 
              />
              <Text style={styles.detailText}>{user.email}</Text>
            </View>
          )}
          {user?.phone && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons 
                name="phone-outline" 
                size={16} 
                color={COLORS.textSecondary} 
              />
              <Text style={styles.detailText}>{user.phone}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Estadísticas rápidas */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>127</Text>
          <Text style={styles.statLabel}>Consultas</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>89</Text>
          <Text style={styles.statLabel}>Clientes</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>4.8</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
      </View>

      {/* Menú de opciones */}
      <View style={styles.menuContainer}>
        {menuItems.map((item) => (
          <MenuItem
            key={item.id}
            title={item.title}
            subtitle={item.subtitle}
            icon={item.icon}
            onPress={item.onPress}
          />
        ))}
      </View>

      {/* Botón de cerrar sesión */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <MaterialCommunityIcons 
            name="logout" 
            size={20} 
            color={COLORS.error} 
          />
          <Text style={styles.logoutText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>

      {/* Información de la app */}
      <View style={styles.appInfo}>
        <Text style={styles.appInfoText}>Salonier v1.0.0</Text>
        <Text style={styles.appInfoText}>
          Desarrollado con ❤️ para profesionales de la belleza
        </Text>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  profileHeader: {
    backgroundColor: COLORS.background,
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: SPACING.md,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.primary,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: COLORS.background,
  },
  userName: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  userRole: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
    marginBottom: SPACING.md,
  },
  userDetails: {
    alignItems: 'center',
    gap: SPACING.xs,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  detailText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    ...SHADOWS.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  statLabel: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    backgroundColor: COLORS.gray200,
    marginHorizontal: SPACING.md,
  },
  menuContainer: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingVertical: SPACING.sm,
    ...SHADOWS.sm,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  logoutContainer: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingVertical: SPACING.sm,
    ...SHADOWS.sm,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  logoutText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.error,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.xs,
  },
  appInfoText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textTertiary,
    textAlign: 'center',
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
});

export default ProfileScreen;
