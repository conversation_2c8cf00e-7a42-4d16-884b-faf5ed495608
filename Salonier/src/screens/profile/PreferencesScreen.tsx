import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { useAuth } from '../../contexts/AuthContext';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';
import { UserPreferences } from '../../types';

type PreferencesScreenNavigationProp = StackNavigationProp<any, 'Preferences'>;

interface Props {
  navigation: PreferencesScreenNavigationProp;
}

const PreferencesScreen: React.FC<Props> = ({ navigation }) => {
  const { user, updateUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferences>(
    user?.preferences || {
      favoriteProducts: [],
      measurementUnits: 'metric',
      notifications: {
        appointments: true,
        clients: true,
        inventory: false,
        marketing: false,
      },
      language: 'es',
      theme: 'light',
    }
  );

  const productBrands = [
    { id: 'wella', name: 'Wella', logo: '🎨' },
    { id: 'loreal', name: "L'Oréal", logo: '💄' },
    { id: 'schwarzkopf', name: 'Schwarzkopf', logo: '✨' },
    { id: 'matrix', name: 'Matrix', logo: '🌟' },
    { id: 'redken', name: 'Redken', logo: '💫' },
    { id: 'goldwell', name: 'Goldwell', logo: '🎭' },
    { id: 'joico', name: 'Joico', logo: '🎪' },
    { id: 'pravana', name: 'Pravana', logo: '🦄' },
  ];

  const languages = [
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
  ];

  const themes = [
    { key: 'light', name: 'Claro', icon: 'white-balance-sunny' },
    { key: 'dark', name: 'Oscuro', icon: 'weather-night' },
    { key: 'auto', name: 'Automático', icon: 'theme-light-dark' },
  ];

  const handleSave = async () => {
    setIsLoading(true);
    
    try {
      await updateUser({
        preferences,
      });

      Alert.alert(
        'Preferencias guardadas',
        'Tus configuraciones se han actualizado correctamente',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudieron guardar las preferencias');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFavoriteProduct = (productId: string) => {
    setPreferences(prev => ({
      ...prev,
      favoriteProducts: prev.favoriteProducts.includes(productId)
        ? prev.favoriteProducts.filter(id => id !== productId)
        : [...prev.favoriteProducts, productId],
    }));
  };

  const updateNotificationSetting = (key: keyof UserPreferences['notifications'], value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value,
      },
    }));
  };

  const SettingSection: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const SettingRow: React.FC<{
    title: string;
    subtitle?: string;
    icon: string;
    rightComponent: React.ReactNode;
  }> = ({ title, subtitle, icon, rightComponent }) => (
    <View style={styles.settingRow}>
      <View style={styles.settingIcon}>
        <MaterialCommunityIcons name={icon as any} size={20} color={COLORS.primary} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      <View style={styles.settingRight}>
        {rightComponent}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Marcas favoritas */}
        <SettingSection title="Marcas Favoritas">
          <Text style={styles.sectionDescription}>
            Selecciona tus marcas preferidas para recomendaciones personalizadas
          </Text>
          <View style={styles.brandsContainer}>
            {productBrands.map((brand) => (
              <TouchableOpacity
                key={brand.id}
                style={[
                  styles.brandChip,
                  preferences.favoriteProducts.includes(brand.id) && styles.brandChipActive
                ]}
                onPress={() => toggleFavoriteProduct(brand.id)}
              >
                <Text style={styles.brandEmoji}>{brand.logo}</Text>
                <Text style={[
                  styles.brandText,
                  preferences.favoriteProducts.includes(brand.id) && styles.brandTextActive
                ]}>
                  {brand.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </SettingSection>

        {/* Unidades de medida */}
        <SettingSection title="Unidades de Medida">
          <SettingRow
            title="Sistema métrico"
            subtitle="Mililitros, gramos, centímetros"
            icon="ruler"
            rightComponent={
              <Switch
                value={preferences.measurementUnits === 'metric'}
                onValueChange={(value) => 
                  setPreferences(prev => ({
                    ...prev,
                    measurementUnits: value ? 'metric' : 'imperial'
                  }))
                }
                trackColor={{ false: COLORS.gray300, true: COLORS.primary + '40' }}
                thumbColor={preferences.measurementUnits === 'metric' ? COLORS.primary : COLORS.gray400}
              />
            }
          />
        </SettingSection>

        {/* Notificaciones */}
        <SettingSection title="Notificaciones">
          <SettingRow
            title="Citas"
            subtitle="Recordatorios y confirmaciones"
            icon="calendar-clock"
            rightComponent={
              <Switch
                value={preferences.notifications.appointments}
                onValueChange={(value) => updateNotificationSetting('appointments', value)}
                trackColor={{ false: COLORS.gray300, true: COLORS.primary + '40' }}
                thumbColor={preferences.notifications.appointments ? COLORS.primary : COLORS.gray400}
              />
            }
          />
          
          <SettingRow
            title="Clientes"
            subtitle="Nuevos clientes y actualizaciones"
            icon="account-group"
            rightComponent={
              <Switch
                value={preferences.notifications.clients}
                onValueChange={(value) => updateNotificationSetting('clients', value)}
                trackColor={{ false: COLORS.gray300, true: COLORS.primary + '40' }}
                thumbColor={preferences.notifications.clients ? COLORS.primary : COLORS.gray400}
              />
            }
          />
          
          <SettingRow
            title="Inventario"
            subtitle="Stock bajo y vencimientos"
            icon="package-variant"
            rightComponent={
              <Switch
                value={preferences.notifications.inventory}
                onValueChange={(value) => updateNotificationSetting('inventory', value)}
                trackColor={{ false: COLORS.gray300, true: COLORS.primary + '40' }}
                thumbColor={preferences.notifications.inventory ? COLORS.primary : COLORS.gray400}
              />
            }
          />
          
          <SettingRow
            title="Marketing"
            subtitle="Promociones y novedades"
            icon="bullhorn"
            rightComponent={
              <Switch
                value={preferences.notifications.marketing}
                onValueChange={(value) => updateNotificationSetting('marketing', value)}
                trackColor={{ false: COLORS.gray300, true: COLORS.primary + '40' }}
                thumbColor={preferences.notifications.marketing ? COLORS.primary : COLORS.gray400}
              />
            }
          />
        </SettingSection>

        {/* Idioma */}
        <SettingSection title="Idioma">
          {languages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={styles.optionRow}
              onPress={() => 
                setPreferences(prev => ({
                  ...prev,
                  language: language.code as 'es' | 'en'
                }))
              }
            >
              <Text style={styles.optionEmoji}>{language.flag}</Text>
              <Text style={styles.optionText}>{language.name}</Text>
              {preferences.language === language.code && (
                <MaterialCommunityIcons 
                  name="check" 
                  size={20} 
                  color={COLORS.primary} 
                />
              )}
            </TouchableOpacity>
          ))}
        </SettingSection>

        {/* Tema */}
        <SettingSection title="Apariencia">
          {themes.map((theme) => (
            <TouchableOpacity
              key={theme.key}
              style={styles.optionRow}
              onPress={() => 
                setPreferences(prev => ({
                  ...prev,
                  theme: theme.key as 'light' | 'dark' | 'auto'
                }))
              }
            >
              <MaterialCommunityIcons 
                name={theme.icon as any} 
                size={20} 
                color={COLORS.textSecondary} 
              />
              <Text style={styles.optionText}>{theme.name}</Text>
              {preferences.theme === theme.key && (
                <MaterialCommunityIcons 
                  name="check" 
                  size={20} 
                  color={COLORS.primary} 
                />
              )}
            </TouchableOpacity>
          ))}
        </SettingSection>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Botón guardar */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Guardando...' : 'Guardar Preferencias'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: COLORS.background,
    marginTop: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    paddingHorizontal: SPACING.lg,
  },
  sectionDescription: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.lg,
    lineHeight: 20,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primary + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  settingSubtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  settingRight: {
    marginLeft: SPACING.md,
  },
  brandsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  brandChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray100,
    borderWidth: 1,
    borderColor: COLORS.gray200,
    gap: SPACING.xs,
  },
  brandChipActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  brandEmoji: {
    fontSize: 16,
  },
  brandText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  brandTextActive: {
    color: COLORS.textInverse,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  optionEmoji: {
    fontSize: 20,
  },
  optionText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.textPrimary,
  },
  buttonContainer: {
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: COLORS.textInverse,
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  bottomSpacing: {
    height: SPACING.xl,
  },
});

export default PreferencesScreen;
