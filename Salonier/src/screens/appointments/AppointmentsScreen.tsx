import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

const AppointmentsScreen: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Datos mock de citas
  const appointments = [
    {
      id: '1',
      time: '09:00',
      duration: 120,
      client: '<PERSON>',
      service: 'Balayage + Corte',
      status: 'confirmed',
      stylist: '<PERSON>',
    },
    {
      id: '2',
      time: '11:30',
      duration: 90,
      client: '<PERSON>',
      service: 'Color + Peinado',
      status: 'in-progress',
      stylist: '<PERSON>',
    },
    {
      id: '3',
      time: '14:00',
      duration: 60,
      client: '<PERSON>',
      service: 'Consulta inicial',
      status: 'scheduled',
      stylist: '<PERSON>',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return COLORS.success;
      case 'in-progress': return COLORS.warning;
      case 'scheduled': return COLORS.info;
      default: return COLORS.gray400;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return 'Confirmada';
      case 'in-progress': return 'En proceso';
      case 'scheduled': return 'Programada';
      default: return 'Desconocido';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header con fecha */}
      <View style={styles.header}>
        <View>
          <Text style={styles.dateText}>
            {selectedDate.toLocaleDateString('es-ES', {
              weekday: 'long',
              day: 'numeric',
              month: 'long',
            })}
          </Text>
          <Text style={styles.appointmentCount}>
            {appointments.length} citas programadas
          </Text>
        </View>
        <TouchableOpacity style={styles.addButton}>
          <MaterialCommunityIcons name="plus" size={24} color={COLORS.textInverse} />
        </TouchableOpacity>
      </View>

      {/* Lista de citas */}
      <ScrollView style={styles.appointmentsList} showsVerticalScrollIndicator={false}>
        {appointments.map((appointment) => (
          <TouchableOpacity key={appointment.id} style={styles.appointmentCard}>
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>{appointment.time}</Text>
              <Text style={styles.durationText}>{appointment.duration}min</Text>
            </View>
            
            <View style={styles.appointmentContent}>
              <View style={styles.appointmentHeader}>
                <Text style={styles.clientName}>{appointment.client}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(appointment.status) + '20' }]}>
                  <Text style={[styles.statusText, { color: getStatusColor(appointment.status) }]}>
                    {getStatusText(appointment.status)}
                  </Text>
                </View>
              </View>
              
              <Text style={styles.serviceText}>{appointment.service}</Text>
              
              <View style={styles.stylistContainer}>
                <MaterialCommunityIcons name="account" size={16} color={COLORS.textSecondary} />
                <Text style={styles.stylistText}>{appointment.stylist}</Text>
              </View>
            </View>

            <TouchableOpacity style={styles.moreButton}>
              <MaterialCommunityIcons name="dots-vertical" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Botón flotante para nueva cita */}
      <TouchableOpacity style={styles.floatingButton}>
        <MaterialCommunityIcons name="calendar-plus" size={28} color={COLORS.textInverse} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    backgroundColor: COLORS.background,
  },
  dateText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    textTransform: 'capitalize',
  },
  appointmentCount: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  appointmentsList: {
    flex: 1,
    padding: SPACING.lg,
  },
  appointmentCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  timeContainer: {
    alignItems: 'center',
    marginRight: SPACING.md,
    minWidth: 60,
  },
  timeText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.primary,
  },
  durationText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  appointmentContent: {
    flex: 1,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  clientName: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  statusText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  serviceText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  stylistContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  stylistText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    color: COLORS.textSecondary,
  },
  moreButton: {
    padding: SPACING.xs,
    marginLeft: SPACING.sm,
  },
  floatingButton: {
    position: 'absolute',
    bottom: SPACING.lg,
    right: SPACING.lg,
    backgroundColor: COLORS.secondary,
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.lg,
  },
});

export default AppointmentsScreen;
