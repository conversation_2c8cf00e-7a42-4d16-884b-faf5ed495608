import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../../constants';

const { width } = Dimensions.get('window');

const AnalyticsScreen: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const periods = [
    { key: 'week', label: 'Semana' },
    { key: 'month', label: 'Mes' },
    { key: 'quarter', label: 'Trimestre' },
    { key: 'year', label: 'Año' },
  ];

  // Datos mock de analíticas
  const analytics = {
    revenue: {
      current: 2450,
      previous: 2180,
      change: 12.4,
    },
    appointments: {
      current: 45,
      previous: 38,
      change: 18.4,
    },
    clients: {
      current: 32,
      previous: 28,
      change: 14.3,
    },
    avgTicket: {
      current: 54.4,
      previous: 57.4,
      change: -5.2,
    },
  };

  const topServices = [
    { name: 'Balayage', revenue: 850, sessions: 12 },
    { name: 'Color completo', revenue: 720, sessions: 18 },
    { name: 'Mechas', revenue: 480, sessions: 8 },
    { name: 'Corrección de color', revenue: 400, sessions: 4 },
  ];

  const MetricCard: React.FC<{
    title: string;
    value: string | number;
    change: number;
    icon: string;
    color: string;
  }> = ({ title, value, change, icon, color }) => (
    <View style={styles.metricCard}>
      <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, { backgroundColor: color + '20' }]}>
          <MaterialCommunityIcons name={icon as any} size={24} color={color} />
        </View>
        <View style={[styles.changeIndicator, { 
          backgroundColor: change >= 0 ? COLORS.success + '20' : COLORS.error + '20' 
        }]}>
          <MaterialCommunityIcons 
            name={change >= 0 ? 'trending-up' : 'trending-down'} 
            size={16} 
            color={change >= 0 ? COLORS.success : COLORS.error}
          />
          <Text style={[styles.changeText, { 
            color: change >= 0 ? COLORS.success : COLORS.error 
          }]}>
            {Math.abs(change).toFixed(1)}%
          </Text>
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Selector de período */}
      <View style={styles.periodSelector}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              selectedPeriod === period.key && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period.key)}
          >
            <Text style={[
              styles.periodText,
              selectedPeriod === period.key && styles.periodTextActive
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Métricas principales */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Métricas Principales</Text>
          <View style={styles.metricsGrid}>
            <MetricCard
              title="Ingresos"
              value={`€${analytics.revenue.current}`}
              change={analytics.revenue.change}
              icon="currency-eur"
              color={COLORS.success}
            />
            <MetricCard
              title="Citas"
              value={analytics.appointments.current}
              change={analytics.appointments.change}
              icon="calendar-clock"
              color={COLORS.primary}
            />
            <MetricCard
              title="Clientes"
              value={analytics.clients.current}
              change={analytics.clients.change}
              icon="account-group"
              color={COLORS.secondary}
            />
            <MetricCard
              title="Ticket Promedio"
              value={`€${analytics.avgTicket.current}`}
              change={analytics.avgTicket.change}
              icon="receipt"
              color={COLORS.accent}
            />
          </View>
        </View>

        {/* Servicios más populares */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Servicios Más Populares</Text>
          <View style={styles.servicesContainer}>
            {topServices.map((service, index) => (
              <View key={service.name} style={styles.serviceItem}>
                <View style={styles.serviceRank}>
                  <Text style={styles.rankNumber}>{index + 1}</Text>
                </View>
                <View style={styles.serviceInfo}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <Text style={styles.serviceSessions}>{service.sessions} sesiones</Text>
                </View>
                <View style={styles.serviceRevenue}>
                  <Text style={styles.revenueAmount}>€{service.revenue}</Text>
                  <View style={styles.revenueBar}>
                    <View 
                      style={[
                        styles.revenueProgress, 
                        { width: `${(service.revenue / topServices[0].revenue) * 100}%` }
                      ]} 
                    />
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Gráfico placeholder */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tendencia de Ingresos</Text>
          <View style={styles.chartPlaceholder}>
            <MaterialCommunityIcons 
              name="chart-line" 
              size={48} 
              color={COLORS.gray300} 
            />
            <Text style={styles.chartPlaceholderText}>
              Gráfico de tendencias
            </Text>
            <Text style={styles.chartPlaceholderSubtext}>
              Próximamente: visualización interactiva de datos
            </Text>
          </View>
        </View>

        {/* Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Insights</Text>
          <View style={styles.insightsContainer}>
            <View style={styles.insightItem}>
              <MaterialCommunityIcons 
                name="lightbulb-outline" 
                size={20} 
                color={COLORS.warning} 
              />
              <Text style={styles.insightText}>
                Los balayages generan el mayor ingreso por sesión
              </Text>
            </View>
            <View style={styles.insightItem}>
              <MaterialCommunityIcons 
                name="trending-up" 
                size={20} 
                color={COLORS.success} 
              />
              <Text style={styles.insightText}>
                Incremento del 18% en citas esta semana
              </Text>
            </View>
            <View style={styles.insightItem}>
              <MaterialCommunityIcons 
                name="account-plus" 
                size={20} 
                color={COLORS.info} 
              />
              <Text style={styles.insightText}>
                4 nuevos clientes adquiridos esta semana
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: COLORS.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    gap: SPACING.sm,
  },
  periodButton: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.gray50,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: COLORS.primary,
  },
  periodText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textSecondary,
  },
  periodTextActive: {
    color: COLORS.textInverse,
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  metricCard: {
    backgroundColor: COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    width: (width - SPACING.lg * 2 - SPACING.md) / 2,
    ...SHADOWS.sm,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    gap: 2,
  },
  changeText: {
    fontSize: TYPOGRAPHY.sizes.xs,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  metricValue: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginBottom: 2,
  },
  metricTitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
  },
  servicesContainer: {
    backgroundColor: COLORS.background,
    marginHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    ...SHADOWS.sm,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray100,
  },
  serviceRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  rankNumber: {
    fontSize: TYPOGRAPHY.sizes.sm,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.primary,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textPrimary,
  },
  serviceSessions: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  serviceRevenue: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  revenueAmount: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
  },
  revenueBar: {
    width: 60,
    height: 4,
    backgroundColor: COLORS.gray200,
    borderRadius: 2,
    marginTop: 4,
  },
  revenueProgress: {
    height: '100%',
    backgroundColor: COLORS.success,
    borderRadius: 2,
  },
  chartPlaceholder: {
    backgroundColor: COLORS.background,
    marginHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.xxl,
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  chartPlaceholderText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.textSecondary,
    marginTop: SPACING.md,
  },
  chartPlaceholderSubtext: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textTertiary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  insightsContainer: {
    backgroundColor: COLORS.background,
    marginHorizontal: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    ...SHADOWS.sm,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  insightText: {
    flex: 1,
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  bottomSpacing: {
    height: SPACING.xxl,
  },
});

export default AnalyticsScreen;
