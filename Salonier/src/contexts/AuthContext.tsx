import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';
import { User } from '../types';
import { authenticateUser, registerUser, currentUser } from '../data/mock/users';
import { STORAGE_KEYS } from '../constants';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  updateUser: (updates: Partial<User>) => Promise<void>;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  role?: User['role'];
  phone?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verificar si hay una sesión guardada al iniciar la app
  useEffect(() => {
    checkStoredAuth();
  }, []);

  const checkStoredAuth = async () => {
    try {
      setIsLoading(true);
      
      // Verificar si hay un token guardado
      const token = await SecureStore.getItemAsync(STORAGE_KEYS.token);
      const storedUser = await SecureStore.getItemAsync(STORAGE_KEYS.user);
      
      if (token && storedUser) {
        const userData = JSON.parse(storedUser);
        setUser(userData);
      } else {
        // Para desarrollo, usar usuario mock por defecto
        if (__DEV__) {
          setUser(currentUser);
          await SecureStore.setItemAsync(STORAGE_KEYS.user, JSON.stringify(currentUser));
          await SecureStore.setItemAsync(STORAGE_KEYS.token, 'mock-token-' + currentUser.id);
        }
      }
    } catch (error) {
      console.error('Error checking stored auth:', error);
      // En caso de error, usar usuario mock en desarrollo
      if (__DEV__) {
        setUser(currentUser);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular delay de red
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const authenticatedUser = authenticateUser(email, password);
      
      if (authenticatedUser) {
        setUser(authenticatedUser);
        
        // Guardar en almacenamiento seguro
        await SecureStore.setItemAsync(STORAGE_KEYS.user, JSON.stringify(authenticatedUser));
        await SecureStore.setItemAsync(STORAGE_KEYS.token, 'mock-token-' + authenticatedUser.id);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular delay de red
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newUser = registerUser({
        name: userData.name,
        email: userData.email,
        role: userData.role || 'stylist',
        phone: userData.phone,
      });
      
      setUser(newUser);
      
      // Guardar en almacenamiento seguro
      await SecureStore.setItemAsync(STORAGE_KEYS.user, JSON.stringify(newUser));
      await SecureStore.setItemAsync(STORAGE_KEYS.token, 'mock-token-' + newUser.id);
      
      return true;
    } catch (error) {
      console.error('Register error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Limpiar almacenamiento seguro
      await SecureStore.deleteItemAsync(STORAGE_KEYS.user);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.token);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.preferences);
      
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = async (updates: Partial<User>): Promise<void> => {
    if (!user) return;
    
    try {
      const updatedUser = {
        ...user,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      
      setUser(updatedUser);
      
      // Actualizar en almacenamiento seguro
      await SecureStore.setItemAsync(STORAGE_KEYS.user, JSON.stringify(updatedUser));
    } catch (error) {
      console.error('Update user error:', error);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook personalizado para usar el contexto de autenticación
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook básico para verificar permisos (mantenido por compatibilidad)
// Para funcionalidad completa, usar usePermissions de hooks/usePermissions.ts
export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // Admin tiene todos los permisos
    if (user.role === 'admin') return true;

    // Verificar permisos específicos por rol
    switch (user.role) {
      case 'stylist':
        return ['consultation', 'clients', 'appointments', 'inventory'].includes(permission);
      case 'receptionist':
        return ['appointments', 'clients'].includes(permission);
      default:
        return false;
    }
  };

  const canAccessModule = (module: string): boolean => {
    return hasPermission(module);
  };

  const isAdmin = user?.role === 'admin';
  const isStylist = user?.role === 'stylist';
  const isReceptionist = user?.role === 'receptionist';

  return {
    hasPermission,
    canAccessModule,
    isAdmin,
    isStylist,
    isReceptionist,
  };
};
