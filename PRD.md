PRD.md
Documento de Especificación de Producto: Salonier (Visión Completa Mejorada)
Versión: 6.0 (Mejorada con Insights de Investigación)
 Fecha: 24 de junio de 2025
 Proyecto: Salonier
1. Introducción
1.1. Propósito de Salonier
Salonier se concibe como una plataforma tecnológica integral y el ecosistema digital definitivo para el profesional de la coloración capilar, estilistas y la gestión de salones de belleza. Su propósito es fusionar la profesión de la peluquería, especialmente de la coloración capilar, con la ciencia y la inteligencia artificial, empoderando a los estilistas para que alcancen nuevos niveles de precisión, eficiencia, creatividad y satisfacción del cliente, siempre dentro de un marco de responsabilidad, cumplimiento normativo, optimización de recursos y excelencia en la experiencia de usuario.
1.2. Visión General de la Aplicación
Salonier será una solución multiplataforma (con una aplicación móvil nativa para Android y iOS como principal punto de interacción para el estilista y versión web para administración y análisis avanzado) que ofrece un asistente de coloración inteligente y gestión de salón. Busca ser el estándar de oro en tecnología para la industria de la belleza, operando con los más altos estándares de privacidad, seguridad, eficiencia y usabilidad.
2. Objetivos del Producto (Visión a Largo Plazo)
Establecer un nuevo estándar de excelencia y precisión en los servicios de coloración capilar a nivel global
Maximizar la rentabilidad y eficiencia operativa de los salones de belleza a través de la automatización inteligente, la optimización de recursos y la toma de decisiones basada en datos
Fomentar una relación más profunda y personalizada entre el estilista y sus clientes, basada en la confianza, la transparencia en el manejo de datos y una experiencia de usuario fluida
Crear una comunidad de profesionales de la belleza que comparten conocimientos y elevan la industria
Convertirse en la herramienta indispensable en la que confían los estilistas para cada aspecto de su trabajo de coloración y gestión
3. Usuarios Objetivo
Estilistas de Coloración Profesionales: Independientes, empleados en salones de todos los tamaños
Dueños y Gerentes de Salones de Belleza: Buscando herramientas para optimizar la gestión, mejorar la rentabilidad, estandarizar la calidad, gestionar personal y analizar el rendimiento del negocio
Asistentes y Recepcionistas de Salón: Utilizando módulos de agenda, comunicación con clientes y facturación básica
(Consideración Futura) Educadores y Academias de Peluquería: Utilizando la plataforma como herramienta de enseñanza y seguimiento de progreso para estudiantes
(Futuro) Clientes Finales del Salón: Accediendo a su historial, información de citas, y recomendaciones personalizadas a través de un portal o app complementaria
4. Funcionalidades Principales (Visión Completa Mejorada de Salonier)
4.1. Autenticación Segura, Roles, Permisos y Personalización del Perfil
Registro y Autenticación:


Registro y login con email/contraseña
Inicio de sesión social (Google, Apple) para mayor comodidad y seguridad
Autenticación de dos factores (2FA) opcional para una capa extra de seguridad
Perfil de Estilista:


Perfil detallado y editable: información personal (nombre completo, teléfono), foto de perfil, información profesional (dirección del salón/trabajo, biografía/experiencia), especialidades y certificaciones
Configuración de preferencias: marcas y líneas de productos favoritas, unidades de medida, configuración de notificaciones, idioma de la interfaz
Gestión de Roles y Permisos (Multi-usuario en Salón):


Roles Predefinidos: Administrador de Salón (dueño/gerente), Estilista, Recepcionista
Permisos Granulares: configuración de acceso a módulos y datos por rol
Estructura jerárquica y segura para salones con múltiples empleados
4.2. Flujo de Consulta de Coloración Ultra-Inteligente Asistido por IA
4.2.1. Selección de Cliente y Protocolo de Seguridad
Selección de Cliente:


Búsqueda y selección fácil del cliente existente o creación de nuevo perfil
Opción de "cliente invitado" para consultas rápidas
Consentimiento Informado:


Recordatorio automático para obtener consentimiento explícito del cliente
Opción de firma digital en smartphone con envío automático por correo
Checklist Interactivo Pre-Servicio: (NUEVA FUNCIONALIDAD)


Verificación automática de seguridad: guantes, materiales desechables, ventilación
Documentación de cumplimiento para auditoría interna
Minimización de riesgos químicos
Workflow de Test de Parche: (NUEVA FUNCIONALIDAD)


Generación de recordatorio automático 48 horas antes del servicio
Enlace directo para que el cliente registre resultado
Productos alternativos sugeridos libres de PPD si hay historial de alergias
4.2.2. Diagnóstico Capilar Exhaustivo (Fase 1) - Mejorado
Captura Multi-Imagen Avanzada:


Feedback en Tiempo Real: (NUEVA FUNCIONALIDAD)
Reconocimiento automático de bordes, foco y luz
Avisos de calidad de foto antes de subida
Validación on-device para asegurar diagnósticos fiables
Guías interactivas para toma óptima de imágenes
Múltiples imágenes (3-5) con etiquetado por zona
Pre-análisis On-Device: (NUEVA FUNCIONALIDAD)
Análisis previo de calidad de imagen
Avisos automáticos por mala iluminación o enfoque
Prevención de rechazos tras subida
Procesamiento para Privacidad:


Detección y difuminado automático de rostros
Compresión inteligente en cliente
Cumplimiento de políticas de IA
Análisis Profundo por IA (Mejorado):


Análisis como experto colorista: nivel natural, subtono, porcentaje de canas, diámetro, densidad, porosidad, elasticidad, resistencia, reflejos existentes
Evaluación del estado de salud general del cabello
Ajuste Dinámico por Historial: (NUEVA FUNCIONALIDAD)
Integración automática de datos previos de coloración
Aprendizaje de patrones específicos del cliente
Recomendaciones basadas en historial completo
Historial Químico del Cliente:


Registro automático de tratamientos previos
Consideración por IA para formulación
Alertas de Alergias Inteligentes: (NUEVA FUNCIONALIDAD)
Detección automática de productos incompatibles
Sugerencias de alternativas libres de alérgenos
4.2.3. Definición Precisa del Color Deseado (Fase 2) - Mejorado
Subida multi imagen de Color Objetivo
Subida de imágenes de referencia con difuminado automático
Análisis de Color Objetivo: Similar al diagnóstico.


Análisis previo de calidad de imagen 
Análisis como experto colorista: nivel natural, subtono, porcentaje de canas, diámetro, densidad, porosidad, elasticidad, resistencia, reflejos existentes
Comparación inteligente entre color actual y deseado ( se muestra 2 fotos, la del color actual y el color deseado)
Calculadora de Corrección de Color: (NUEVA FUNCIONALIDAD CLAVE)


Herramienta de análisis "color target vs. base real"
Simulación de pasos de corrección secuenciales
Recomendaciones automáticas de productos y tiempos de exposición
Visualización paso a paso del proceso de corrección
Herramientas de Selección:


Paletas digitales profesionales
Análisis IA de imágenes de referencia
Selección de técnica de aplicación
(Futuro) Vista Previa AR:


Efectos en tiempo real directamente en app de estilista
Alineación de expectativas con cliente antes de formular
Simulación de resultados esperados
4.2.4. Formulación Experta y Segura Asistida por IA (Fase 3) - Revolucionado
Motor de Formulación Inteligente:


Análisis completo de diagnóstico, color deseado, historial químico
Consideración de marcas/líneas preferidas y productos en stock
Módulo de Conversión de Marcas: (NUEVA FUNCIONALIDAD CLAVE)


Equivalencias automáticas entre tintes de distintas líneas
Base de datos de conversiones profesionales
Adaptación automática según disponibilidad de productos
Generación de Fórmula Detallada:


Productos exactos por líneas y marcas preferidas
Proporciones precisas, volúmenes, tiempos de pose
Instrucciones paso a paso personalizadas
Análisis de Viabilidad y Riesgo:


Evaluación de salud capilar y alcanzabilidad
Alertas de incompatibilidades automáticas
Sugerencias de tratamientos pre/post
Desglose de Costes por Mezcla: (NUEVA FUNCIONALIDAD)


Cálculo automático de coste de materiales
Transparencia total para facturación al cliente
Optimización de rentabilidad por servicio
4.2.5. Documentación Exhaustiva del Servicio (Fase 4) - Optimizada
Documentación Inteligente:


Plantillas Inteligentes: (NUEVA FUNCIONALIDAD)
Auto-llenado de notas frecuentes
Etiquetado automático por palabras clave
Agilización del cierre de servicio
Subida de fotos del resultado con difuminado automático
Registro de fórmula final aplicada
Registro de Satisfacción:


Notas detalladas del estilista
Comentarios del cliente
Valoración opcional de satisfacción
Vinculación automática al historial y cita
4.3. Gestión Integral de Clientes - Ampliada
Perfiles de Cliente 360°:


Datos de contacto completos
Historial exhaustivo: fechas, diagnósticos, fórmulas, fotos, productos, notas, costes
Tests de alergia y sensibilidades
Preferencias personales y cumpleaños
Gestión de Alergias Avanzada:


Registro detallado de reacciones previas
Sistema de alertas automáticas
Base de datos de productos alternativos
Galería de Inspiración:


Colección personalizada por cliente
Historial visual de transformaciones
Referencias para futuras citas
4.4. Gestión de Citas y Calendario - Optimizada
Calendario Visual Inteligente:


Vistas diaria, semanal, mensual con filtros por empleado
Programación rápida con cálculo automático de duración
Gestión de tiempos de procesamiento
Gestión Multi-Estilista:


Agenda con disponibilidad múltiple
Asignación inteligente de citas
Vista consolidada para administradores
Estados y Notificaciones:


Confirmaciones automáticas
Lista de espera inteligente
Reprogramaciones y cancelaciones
4.5. Gestión de Marcas/Líneas Preferidas e Inventario - Revolucionada
Catálogo Digital Inteligente:


Productos de coloración, tratamientos, styling, reventa
Entrada asistida por IA: auto-rellenado por nombre/código de barras
Gestión de marcas y líneas preferidas por usuario/salón
(Futuro) Integración con Hardware:


Básculas Bluetooth para medición en tiempo real
Ajuste automático de stock
Seguimiento preciso de consumo
Control de Stock Avanzado:


Alertas de stock bajo
Predicción de necesidades
Gestión de proveedores
4.6. Catálogo de Servicios y Precios Inteligentes - Revolucionado
Gestión de Servicios:


Creación ilimitada de servicios personalizados
Asignación de precio base, duración, productos estándar
Agrupación en paquetes
Algoritmo de Margen Recomendado: (NUEVA FUNCIONALIDAD CLAVE)


Cálculo automático basado en materia prima y tiempo
Rangos de precio sugeridos para maximizar rentabilidad
Optimización de márgenes por servicio
Precios Dinámicos:


Ajuste según diagnóstico IA (longitud, densidad, complejidad)
Transparencia en desglose de costes
Facturación detallada al cliente
4.7. Analíticas Avanzadas y Reportes de Negocio - Expandidas
Dashboard Principal Mejorado:


KPIs personalizables: ingresos, citas, ocupación, clientes nuevos
Métricas de rentabilidad en tiempo real
Dashboard de Rentabilidad por Servicio: (NUEVA FUNCIONALIDAD CLAVE)


Margen neto por servicio
Análisis coste de producto vs. precio
Alertas de servicios por debajo de umbral de rentabilidad
Optimización de precios basada en datos
Reportes Financieros Detallados:


Análisis de rentabilidad por estilista
Seguimiento de costes de materiales
Proyecciones de ingresos
Análisis de Clientes Profundo:


Frecuencia, gasto, retención, LTV
Segmentación inteligente
Análisis de satisfacción
Optimización de Inventario:


Reportes de uso y rotación
Análisis de desperdicio
Recomendaciones de compra
4.8. Módulo de Formación y Comunidad - Elevado a MVP
Biblioteca de Recursos (MVP): (FUNCIONALIDAD ELEVADA)


Tutoriales de correcciones y técnicas avanzadas
Casos basados en problemas reales
Técnicas de coloración fantasía y matices metálicos
Guías de tendencias actualizadas
Comunidad Profesional:


Foro para consultas y casos
Intercambio de experiencias
Actualizaciones sobre tendencias
(Futuro) Gamificación y Mentorías:


Sistema de badges por especialidad
"Consultas destacadas" de la comunidad
Conexión directa con expertos para casos críticos
Certificaciones y seguimiento de progreso
4.9. Portal/App para Cliente Final (Visión Futura)
Acceso a historial personal
Gestión de citas
Recomendaciones personalizadas
Galería de sus transformaciones
4.10. Módulo de Gestión de Personal (Para Salones)
Perfiles de Empleados:


Gestión de estilistas y asistentes
Asignación de roles y permisos
Gestión de horarios y turnos
(Futuro) Análisis de Rendimiento:


Cálculo de comisiones
Seguimiento de objetivos
Métricas de productividad
5. Arquitectura Técnica (Visión Completa Mejorada)
5.1. Frontend
Aplicación Móvil Principal (Estilistas):


React Native con Expo SDK
Componentización robusta
Gestión de estado escalable (Context API, considerando Zustand/Redux Toolkit)
Capacidades de IA On-Device:
Tensorflow.js para análisis de calidad de imagen
Detección facial para difuminado
Validación en tiempo real
Optimización de Rendimiento:


Esqueletos de UI
Carga progresiva
Actualizaciones optimistas
Virtualización de listas
Almacenamiento Local:


AsyncStorage o MMKV para caché
Datos de sesión y preferencias
Funcionamiento offline básico
5.2. Backend
Plataforma Principal:


Supabase (Auth, Database, Storage, Edge Functions)
PostgreSQL optimizado
Estrategias de caché robustas
Escalabilidad y Optimización:


Arquitectura orientada a eventos
Monitoreo de consultas
Optimización continua de BD
Políticas de almacenamiento eficientes
5.3. Integración IA - Expandida
Modelos de IA:


OpenAI (GPT-4 Vision, GPT-4 Turbo)
Cumplimiento estricto de políticas
Orquestación de múltiples modelos
Optimización de Costes IA:


Prompts específicos y dirigidos
Procesamiento por lotes cuando aplique
Caching de respuestas IA
Monitoreo activo de costes
IA Especializada:


Motor de Conversión de Marcas: Base de datos inteligente de equivalencias
Algoritmo de Precios: Optimización de márgenes basada en IA
Análisis de Rentabilidad: Predicciones y recomendaciones automáticas
(Futuro) Capacidades Avanzadas:


Fine-tuning de modelos especializados
IA explicable (XAI)
Personalización por salón/estilista
6. Modelo de Datos (Visión Completa Expandida)
El esquema de base de datos se ha expandido significativamente para soportar:
Nuevas Tablas Principales:


brand_conversions: Equivalencias entre marcas y productos
cost_breakdowns: Desglose detallado de costes por servicio
allergy_profiles: Perfiles detallados de alergias y sensibilidades
safety_checklists: Registros de verificaciones de seguridad
patch_test_workflows: Gestión de tests de parche y seguimiento
Campos Adicionales:


Consentimiento y logs de auditoría
Indicadores de anonimización de imágenes
Métricas de rentabilidad y márgenes
Configuraciones de precios dinámicos
Datos para analíticas avanzadas
Optimización:


Índices optimizados para consultas frecuentes
Vistas materializadas para reportes
Estrategias de particionado para escalabilidad
7. Requisitos No Funcionales (Visión Completa Expandida)
7.1. Rendimiento Superior y Escalabilidad
Respuesta < 2 segundos para operaciones críticas
Soporte para crecimiento exponencial de usuarios
Optimización continua de consultas y recursos
7.2. Seguridad de Grado Empresarial
Validación Rigurosa: Frontend y backend
Seguridad Móvil: Almacenamiento seguro con expo-secure-store
Gestión de Sesiones: Tokens JWT con políticas de expiración
Auditorías Regulares: Pen-testing y revisión de vulnerabilidades
7.3. Privacidad y Cumplimiento Normativo
Privacy by Design: Diseño orientado a la privacidad desde el inicio
Cumplimiento: GDPR, CCPA y regulaciones específicas
Transparencia: Términos de servicio y políticas de privacidad claras
Minimización de Datos: Solo datos necesarios para funcionalidad
7.4. Alta Disponibilidad y Fiabilidad
Uptime: 99.9%+ garantizado
Recuperación: Plan de recuperación ante desastres (DRP)
Continuidad: Plan de continuidad del negocio (BCP)
Monitoreo: Supervisión 24/7 de sistemas críticos
7.5. Experiencia de Usuario Excepcional
Onboarding Guiado: Introducción contextual y progresiva
Ayuda Integrada: FAQs y soporte en contexto
Accesibilidad: Cumplimiento de pautas WCAG
Diseño Adaptativo: Revelación gradual de complejidad
7.6. Mantenibilidad y Evolución
Pruebas Automatizadas: Unitarias, integración, E2E
Documentación Técnica: Siempre actualizada
Logging Estructurado: Monitoreo y debugging eficiente
Arquitectura Modular: Facilita evolución y mantenimiento
7.7. Operaciones Offline (Consideración Estratégica)
Funcionalidades Clave Offline: Visualización de datos cacheados
Sincronización Inteligente: Manejo de conflictos automático
Almacenamiento Local: Optimizado para datos críticos
8. Roadmap de Implementación
Fase 1 - MVP Mejorado (Meses 1-6)
Funcionalidades Principales:
Flujo de consulta completo con IA
Módulo de conversión de marcas
Pre-análisis on-device de calidad de imagen
Workflow de test de parche
Checklist de seguridad interactivo
Dashboard de rentabilidad básico
Algoritmo de margen recomendado
Plantillas inteligentes de documentación
Biblioteca de tutoriales avanzados
Fase 2 - Expansión Profesional (Meses 7-12)
Funcionalidades Avanzadas:
Analíticas avanzadas completas
Gestión multi-salón
API para integraciones
Optimizaciones de rendimiento
Seguridad empresarial completa
Fase 3 - Innovación Futura (Año 2+)
Tecnologías Emergentes:
Vista previa AR para consultas
Integración con básculas Bluetooth
Gamificación y mentorías
Portal para clientes finales
IA personalizada por salón
Expansión internacional
9. Métricas de Éxito
Métricas de Adopción
Usuarios activos mensuales
Retención a 30, 60, 90 días
Tiempo promedio en aplicación
Frecuencia de uso de funcionalidades clave
Métricas de Negocio
Incremento en rentabilidad por servicio
Reducción de desperdicio de productos
Mejora en satisfacción del cliente
Optimización de tiempos de servicio
Métricas Técnicas
Tiempo de respuesta de IA
Precisión de diagnósticos
Uptime del sistema
Coste por transacción de IA
10. Conclusión
Esta especificación mejorada de Salonier incorpora las mejoras identificadas en la investigación de campo, creando una solución integral que no solo aborda las necesidades actuales de los profesionales de coloración capilar, sino que también establece las bases para la innovación futura en la industria de la belleza.
Las mejoras implementadas se enfocan en resolver problemas reales: desde la conversión automática entre marcas hasta la optimización de rentabilidad, pasando por protocolos de seguridad mejorados y herramientas de formación continua. Esto posiciona a Salonier como la plataforma definitiva para la transformación digital de la industria de la coloración capilar.

